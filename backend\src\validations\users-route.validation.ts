// JSON Schema definitions for Users routes - Body and Query validation only
import { PAGINATION, VALIDATION_RULES } from '@/constants';

// GET /users - Get all users with pagination and filtering
export const getUsersSchema = {
  querystring: {
    type: 'object',
    properties: {
      page: { type: 'number', minimum: 1, default: PAGINATION.DEFAULT_PAGE },
      limit: {
        type: 'number',
        minimum: 1,
        maximum: PAGINATION.MAX_LIMIT,
        default: PAGINATION.DEFAULT_LIMIT,
      },
      search: { type: 'string' },
      isActive: { type: 'boolean' },
    },
  },
};

// GET /users/:id - Get user by ID
export const getUserByIdSchema = {
  params: {
    type: 'object',
    properties: {
      id: { type: 'string', format: 'uuid' },
    },
    required: ['id'],
  },
};

// POST /users - Create a new user
export const createUserSchema = {
  body: {
    type: 'object',
    properties: {
      email: { type: 'string', format: 'email' },
      name: { type: 'string', minLength: 1, maxLength: 100 },
      password: {
        type: 'string',
        minLength: VALIDATION_RULES.PASSWORD_MIN_LENGTH,
      },
      role: {
        type: 'string',
        enum: ['user', 'influencer', 'brand', 'agency', 'admin'],
      },
    },
    required: ['email', 'name', 'password'],
  },
};

// PUT /users/:id - Update user by ID
export const updateUserSchema = {
  params: {
    type: 'object',
    properties: {
      id: { type: 'string', format: 'uuid' },
    },
    required: ['id'],
  },
  body: {
    type: 'object',
    properties: {
      email: { type: 'string', format: 'email' },
      name: { type: 'string', minLength: 1, maxLength: 100 },
      isActive: { type: 'boolean' },
      password: {
        type: 'string',
        minLength: VALIDATION_RULES.PASSWORD_MIN_LENGTH,
      },
      role: {
        type: 'string',
        enum: ['user', 'influencer', 'brand', 'agency', 'admin'],
      },
    },
    // No required fields for update - all optional
  },
};

// DELETE /users/:id - Delete user by ID
export const deleteUserSchema = {
  params: {
    type: 'object',
    properties: {
      id: { type: 'string', format: 'uuid' },
    },
    required: ['id'],
  },
};
