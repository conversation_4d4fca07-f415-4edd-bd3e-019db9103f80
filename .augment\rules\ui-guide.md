---
type: "agent_requested"
description: "Example description"
---

# UI Guide for AI-Based Influencer Marketing Dashboard

This guide outlines the design principles, color scheme, typography, and component recommendations for building an AI-based Influencer Marketing tool using React Router v7, Tailwind CSS v4, and Shadcn UI. The goal is to create a modern, professional, and visually appealing dashboard that caters to influencers and marketers, providing real-time insights, trending content analysis, and an influencer marketplace.

## 1. Color Scheme

The color scheme is designed to be accessible, vibrant, and aligned with social media aesthetics. It builds on your existing Tailwind CSS configuration, which uses Oklch for accessibility, and introduces additional accent colors for engagement.

### Light Mode

| Variable       | Value                        | Description                                                                      |
| -------------- | ---------------------------- | -------------------------------------------------------------------------------- |
| `--primary`    | `oklch(0.623 0.214 259.815)` | Blue, used for primary buttons and headers, conveying trust and professionalism. |
| `--secondary`  | `oklch(0.967 0.001 286.375)` | Light gray, for secondary elements like backgrounds or less prominent text.      |
| `--accent`     | `oklch(0.967 0.001 286.375)` | Light gray, for subtle highlights.                                               |
| `--accent-2`   | `oklch(0.5 0.2 280)`         | Purple, for creative highlights (e.g., "Boost Post" buttons).                    |
| `--accent-3`   | `oklch(0.5 0.2 40)`          | Orange, for energetic highlights (e.g., key metrics).                            |
| `--background` | `oklch(1 0 0)`               | White, for main backgrounds.                                                     |
| `--foreground` | `oklch(0.141 0.005 285.823)` | Dark gray, for text.                                                             |

### Dark Mode

| Variable       | Value                        | Description                        |
| -------------- | ---------------------------- | ---------------------------------- |
| `--primary`    | `oklch(0.546 0.245 262.881)` | Darker blue, for primary elements. |
| `--secondary`  | `oklch(0.274 0.006 286.033)` | Dark gray, for secondary elements. |
| `--accent`     | `oklch(0.274 0.006 286.033)` | Dark gray, for subtle highlights.  |
| `--accent-2`   | `oklch(0.6 0.2 280)`         | Lighter purple, for highlights.    |
| `--accent-3`   | `oklch(0.6 0.2 40)`          | Lighter orange, for highlights.    |
| `--background` | `oklch(0.141 0.005 285.823)` | Dark gray, for backgrounds.        |
| `--foreground` | `oklch(0.985 0 0)`           | White, for text.                   |

### Usage

- **Primary**: Use for primary buttons (e.g., "Connect Account"), headers, and key UI elements.
- **Secondary**: Use for secondary buttons (e.g., "View Details") or less prominent text.
- **Accents**: Use `--accent-2` (purple) and `--accent-3` (orange) for interactive elements like "Boost Post" or to highlight key metrics (e.g., engagement rates).
- **Background/Foreground**: Use for main layout and text to ensure readability.
- **Accessibility**: Verify contrast ratios using tools like [WebAIM Contrast Checker](https://webaim.org/resources/contrastchecker/) to ensure accessibility.

## 2. CSS Updates

Your existing Tailwind CSS file is well-structured. Below are the necessary changes to incorporate the new accent colors.

### Updated CSS

```css
:root {
	--accent-2: oklch(0.5 0.2 280); /* Purple */
	--accent-3: oklch(0.5 0.2 40); /* Orange */
}

.dark {
	--accent-2: oklch(0.6 0.2 280); /* Lighter purple */
	--accent-3: oklch(0.6 0.2 40); /* Lighter orange */
}

@layer base {
	* {
		@apply border-border outline-ring/50;
	}
	body {
		@apply bg-background text-foreground font-sans;
	}
	h1 {
		@apply text-4xl font-semibold;
	}
	h2 {
		@apply text-3xl font-semibold;
	}
	h3 {
		@apply text-2xl font-semibold;
	}
	p {
		@apply text-base font-normal;
	}
	button {
		@apply text-base font-medium;
	}
}
```

### Integration with Tailwind

- Add the Inter font to your Tailwind configuration:
  ```js
  // tailwind.config.js
  module.exports = {
  	theme: {
  		extend: {
  			fontFamily: {
  				sans: ["Inter", "sans-serif"],
  			},
  		},
  	},
  };
  ```
- Include Inter via Google Fonts in your HTML:
  ```html
  <link
  	href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap"
  	rel="stylesheet"
  />
  ```

### Component Styling

- **Primary Button**:
  ```css
  .btn-primary {
  	@apply bg-primary text-primary-foreground rounded-lg px-4 py-2;
  }
  ```
- **Accent Button**:
  ```css
  .btn-accent {
  	@apply bg-accent-2 text-accent-foreground rounded-lg px-4 py-2;
  }
  ```

## 3. Typography

The typography should be clean and modern to appeal to influencers and marketers.

### Font Family

- **Font**: Inter (available via [Google Fonts](https://fonts.google.com/specimen/Inter))
- **Reason**: Inter is highly legible, modern, and versatile, with multiple weights for clear hierarchy.

### Font Weights and Sizes

| Element | Weight | Size   | Tailwind Class           |
| ------- | ------ | ------ | ------------------------ |
| h1      | 600    | 2.5rem | `text-4xl font-semibold` |
| h2      | 600    | 2rem   | `text-3xl font-semibold` |
| h3      | 600    | 1.5rem | `text-2xl font-semibold` |
| Body    | 400    | 1rem   | `text-base font-normal`  |
| Buttons | 500    | 1rem   | `text-base font-medium`  |

### Implementation

- Use Tailwind utility classes for consistency.
- Example:
  ```html
  <h1 class="text-4xl font-semibold">Dashboard</h1>
  <p class="text-base font-normal">Welcome to your influencer marketing tool.</p>
  ```

## 4. Radius

- **Value**: `--radius: 0.65rem` (as defined in your CSS).
- **Usage**: Apply to cards, buttons, and inputs for a modern, slightly rounded look.
- **Example**:
  ```css
  .card {
  	@apply rounded-lg bg-card text-card-foreground p-4;
  }
  ```

## 5. Spacing

- Use Tailwind’s default spacing scale for consistency:
  - **Padding**: `p-4` (1rem) for cards, `p-2` for smaller elements.
  - **Margins**: `m-4` for sections, `m-2` for smaller gaps.
  - **Grid Gaps**: `gap-4` or `gap-6` for grid layouts.
- Example:
  ```html
  <div class="grid grid-cols-1 md:grid-cols-2 gap-6 p-4">
  	<!-- Card components -->
  </div>
  ```

## 6. Components

Leverage Shadcn UI components for consistency and speed. Customize them with your color variables.

### Recommended Components

| Component   | Purpose                                                     | Example                                                                        |
| ----------- | ----------------------------------------------------------- | ------------------------------------------------------------------------------ |
| **Cards**   | Display influencer profiles, campaign metrics, or insights. | `<div class="card">Influencer Name</div>`                                      |
| **Tables**  | List data like campaign performance or influencer stats.    | Use Shadcn’s `Table` component.                                                |
| **Charts**  | Visualize metrics (e.g., engagement, reach).                | Use [Chart.js](https://www.chartjs.org/) or [Recharts](https://recharts.org/). |
| **Forms**   | Input data for campaigns or account connections.            | Use Shadcn’s `Input` and `Form` components.                                    |
| **Buttons** | Trigger actions like "Connect Account" or "Boost Post."     | `<button class="btn-primary">Connect</button>`                                 |

## 7. Layout

- **Sidebar**: Use for navigation (e.g., Dashboard, Campaigns, Influencers, Marketplace).
- **Main Content**: Display dashboards, charts, and tables.
- **Responsive Design**: Use Tailwind’s responsive utilities (e.g., `md:grid-cols-2`).
- Example:
  ```html
  <div class="flex">
  	<aside class="w-64 bg-sidebar text-sidebar-foreground p-4">
  		<!-- Navigation -->
  	</aside>
  	<main class="flex-1 p-4">
  		<!-- Dashboard content -->
  	</main>
  </div>
  ```

## 8. Icons

- **Icon Set**: Use [Heroicons](https://heroicons.com/) for a consistent, modern look.
- **Usage**: For navigation, buttons, or to highlight metrics.
- Example:
  ```html
  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
  	<path
  		stroke-linecap="round"
  		stroke-linejoin="round"
  		stroke-width="2"
  		d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
  	/>
  </svg>
  ```

## 9. Buttons

- **Primary**: Use `--primary` for key actions (e.g., "Connect Account").
- **Secondary**: Use `--secondary` for less prominent actions (e.g., "Cancel").
- **Accent**: Use `--accent-2` or `--accent-3` for engaging actions (e.g., "Boost Post").
- Example:
  ```html
  <button class="btn-primary">Connect Account</button>
  <button class="btn-accent">Boost Post</button>
  ```

## 10. Forms

- Use Shadcn UI’s form components for consistency.
- Include labels and help text for clarity.
- Example:
  ```html
  <label class="block text-sm font-medium text-foreground">Username</label>
  <input
  	type="text"
  	class="mt-1 block w-full rounded-md border-border shadow-sm focus:ring focus:ring-ring/50"
  />
  ```

## 11. Data Visualization

- **Purpose**: Display key metrics like engagement rates, reach, impressions, and conversions.
- **Libraries**: Use [Chart.js](https://www.chartjs.org/) or [Recharts](https://recharts.org/) for responsive charts.
- **Accessibility**: Add tooltips and labels to charts.
- Example Metrics:
  - Bar chart for campaign performance.
  - Line chart for engagement trends.
  - Pie chart for audience demographics.

## 12. Accessibility

- **Contrast**: Ensure text meets WCAG 2.1 contrast ratios (use [WebAIM](https://webaim.org/resources/contrastchecker/)).
- **Semantic HTML**: Use `<nav>`, `<main>`, `<button>`, etc.
- **Keyboard Navigation**: Ensure all interactive elements are accessible via keyboard.
- **Alt Text**: Add for images and icons.

## 13. Navigation with React Router v7

- Use React Router v7 for client-side routing.
- Example:

  ```jsx
  import { BrowserRouter, Routes, Route } from "react-router-dom";

  function App() {
  	return (
  		<BrowserRouter>
  			<div class="flex">
  				<aside class="w-64 bg-sidebar text-sidebar-foreground p-4">
  					<nav>
  						<a href="/" class="block py-2">
  							Dashboard
  						</a>
  						<a href="/campaigns" class="block py-2">
  							Campaigns
  						</a>
  						<a href="/influencers" class="block py-2">
  							Influencers
  						</a>
  					</nav>
  				</aside>
  				<main class="flex-1 p-4">
  					<Routes>
  						<Route path="/" element={<Dashboard />} />
  						<Route path="/campaigns" element={<Campaigns />} />
  						<Route path="/influencers" element={<Influencers />} />
  					</Routes>
  				</main>
  			</div>
  		</BrowserRouter>
  	);
  }
  ```

## 14. Best Practices

- **Consistency**: Use Shadcn UI components and Tailwind utilities for a cohesive look.
- **Responsiveness**: Test on various screen sizes using Tailwind’s responsive classes.
- **Performance**: Optimize images and charts for fast loading.
- **User Feedback**: Include tooltips and loading states for better UX.

## Citations

- Color scheme recommendations are based on design principles for social media and dashboard interfaces, emphasizing accessibility and vibrancy.
- Typography choice (Inter) is based on its widespread use in modern web applications for legibility and aesthetics.
- UI guidelines are derived from best practices for dashboard design and influencer marketing tools, ensuring usability and engagement.
