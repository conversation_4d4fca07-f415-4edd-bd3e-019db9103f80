import type { Route } from "./+types/home";
import { useSession, signOut } from "~/lib/auth-client";
import { Link, useNavigate } from "react-router";

export function meta({}: Route.MetaArgs) {
	return [
		{ title: "Influencer Marketing Platform" },
		{ name: "description", content: "Connect brands with influencers" },
	];
}

export default function Home() {
	const { data: session, isPending } = useSession();
	const navigate = useNavigate();

	const handleSignOut = async () => {
		await signOut();
		navigate("/");
	};

	if (isPending) {
		return <div className="p-8">Loading...</div>;
	}

	return (
		<main className="min-h-screen bg-gray-50 p-8">
			<div className="max-w-4xl mx-auto">
				<h1 className="text-4xl font-bold text-gray-900 mb-8">Influencer Marketing Platform</h1>

				{session?.user ? (
					<div className="bg-white rounded-lg shadow p-6">
						<h2 className="text-2xl font-semibold mb-4">Welcome back!</h2>
						<p className="text-gray-600 mb-4">
							Hello, {session.user.name} ({session.user.email})
						</p>
						<p className="text-sm text-gray-500 mb-4">Role: {session.user.role}</p>
						<button
							onClick={handleSignOut}
							className="bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
						>
							Sign Out
						</button>
					</div>
				) : (
					<div className="bg-white rounded-lg shadow p-6">
						<h2 className="text-2xl font-semibold mb-4">Get Started</h2>
						<p className="text-gray-600 mb-6">
							Sign in to access your dashboard and connect with brands or influencers.
						</p>
						<div className="space-x-4">
							<Link
								to="/auth/login"
								className="bg-indigo-600 text-white px-6 py-2 rounded hover:bg-indigo-700 inline-block"
							>
								Sign In
							</Link>
							<Link
								to="/auth/register"
								className="bg-gray-600 text-white px-6 py-2 rounded hover:bg-gray-700 inline-block"
							>
								Sign Up
							</Link>
						</div>
					</div>
				)}
			</div>
		</main>
	);
}
