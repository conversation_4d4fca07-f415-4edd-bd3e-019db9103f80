version: '3.8'

services:
  app:
    build:
      context: .
      target: development
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=development
      - PORT=8080
      - JWT_SECRET=development-jwt-secret-key-change-in-production
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis

  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: influencer_marketing
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data: