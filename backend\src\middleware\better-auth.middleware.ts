import { FastifyRequest, FastifyReply } from 'fastify';
import { auth } from '@/lib/auth';

export async function requireAuth(request: FastifyRequest, reply: FastifyReply) {
  const session = await auth.api.getSession({
    headers: request.headers as any,
  });

  if (!session) {
    return reply.status(401).send({ error: 'Unauthorized' });
  }

  request.user = session.user;
  request.session = session.session;
}

export async function requireAdmin(request: FastifyRequest, reply: FastifyReply) {
  await requireAuth(request, reply);
  
  if (request.user?.role !== 'admin') {
    return reply.status(403).send({ error: 'Admin access required' });
  }
}

export async function requireSelfOrAdmin(request: FastifyRequest, reply: FastifyReply) {
  await requireAuth(request, reply);
  
  const { id } = request.params as { id: string };
  if (request.user?.id !== id && request.user?.role !== 'admin') {
    return reply.status(403).send({ error: 'Access denied' });
  }
}

declare module 'fastify' {
  interface FastifyRequest {
    user?: any;
    session?: any;
  }
}