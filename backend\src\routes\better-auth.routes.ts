import { FastifyInstance } from 'fastify';
import { requireAuth } from '@/middleware/better-auth.middleware';

export default async function betterAuthRoutes(fastify: FastifyInstance) {
  // GET /auth/me - Get current user info (Protected route)
  fastify.get('/me', {
    onRequest: [requireAuth],
  }, async (request, reply) => {
    return reply.send({
      success: true,
      data: {
        user: request.user,
        session: request.session,
      },
    });
  });
}