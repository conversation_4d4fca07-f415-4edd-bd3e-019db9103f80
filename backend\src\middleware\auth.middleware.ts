import { FastifyRequest, FastifyReply } from 'fastify';
import { AUTH_MESSAGES } from '@/constants';
import { AuthUser } from '@/plugins/auth.plugin';

/**
 * Middleware factory for creating authentication middleware with different requirements
 */
export class AuthMiddleware {
  /**
   * Require authentication - user must be logged in
   */
  static requireAuth() {
    return async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        await request.jwtVerify();

        if (!request.user) {
          return reply.status(401).send({
            success: false,
            error: AUTH_MESSAGES.UNAUTHORIZED,
          });
        }
      } catch (err) {
        return reply.status(401).send({
          success: false,
          error: AUTH_MESSAGES.TOKEN_INVALID,
        });
      }
    };
  }

  /**
   * Require active user - user must be logged in and active
   */
  static requireActiveUser() {
    return async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        await request.jwtVerify();

        const authUser = request.user as AuthUser | undefined;
        if (!authUser) {
          return reply.status(401).send({
            success: false,
            error: AUTH_MESSAGES.UNAUTHORIZED,
          });
        }

        if (!authUser.isActive) {
          return reply.status(403).send({
            success: false,
            error: AUTH_MESSAGES.ACCOUNT_INACTIVE,
          });
        }
      } catch (err) {
        return reply.status(401).send({
          success: false,
          error: AUTH_MESSAGES.TOKEN_INVALID,
        });
      }
    };
  }

  /**
   * Require specific roles - user must be logged in, active, and have one of the specified roles
   */
  static requireRoles(allowedRoles: string[]) {
    return async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        await request.jwtVerify();

        const authUser = request.user as AuthUser | undefined;
        if (!authUser) {
          return reply.status(401).send({
            success: false,
            error: AUTH_MESSAGES.UNAUTHORIZED,
          });
        }

        if (!authUser.isActive) {
          return reply.status(403).send({
            success: false,
            error: AUTH_MESSAGES.ACCOUNT_INACTIVE,
          });
        }

        if (!allowedRoles.includes(authUser.role)) {
          return reply.status(403).send({
            success: false,
            error: AUTH_MESSAGES.FORBIDDEN,
          });
        }
      } catch (err) {
        return reply.status(401).send({
          success: false,
          error: AUTH_MESSAGES.TOKEN_INVALID,
        });
      }
    };
  }

  /**
   * Optional authentication - sets user if token is valid, but doesn't require it
   */
  static optionalAuth() {
    return async (request: FastifyRequest, _reply: FastifyReply) => {
      try {
        await request.jwtVerify();
        // User is set automatically by jwtVerify if successful
      } catch (err) {
        // Don't throw error, just continue without user
        (request as any).user = undefined;
      }
    };
  }

  /**
   * Admin only - user must be admin
   */
  static adminOnly() {
    return AuthMiddleware.requireRoles(['admin']);
  }

  /**
   * Brand or admin - user must be brand or admin
   */
  static brandOrAdmin() {
    return AuthMiddleware.requireRoles(['brand', 'admin']);
  }

  /**
   * Influencer or admin - user must be influencer or admin
   */
  static influencerOrAdmin() {
    return AuthMiddleware.requireRoles(['influencer', 'admin']);
  }

  /**
   * Agency or admin - user must be agency or admin
   */
  static agencyOrAdmin() {
    return AuthMiddleware.requireRoles(['agency', 'admin']);
  }

  /**
   * Self or admin - user can only access their own resources or be admin
   */
  static selfOrAdmin(userIdParam: string = 'id') {
    return async (request: FastifyRequest, reply: FastifyReply) => {
      try {
        await request.jwtVerify();

        const authUser = request.user as AuthUser | undefined;
        if (!authUser) {
          return reply.status(401).send({
            success: false,
            error: AUTH_MESSAGES.UNAUTHORIZED,
          });
        }

        if (!authUser.isActive) {
          return reply.status(403).send({
            success: false,
            error: AUTH_MESSAGES.ACCOUNT_INACTIVE,
          });
        }

        // Admin can access any resource
        if (authUser.role === 'admin') {
          return;
        }

        // Check if user is accessing their own resource
        const params = request.params as Record<string, string>;
        const resourceUserId = params[userIdParam];

        if (authUser.id !== resourceUserId) {
          return reply.status(403).send({
            success: false,
            error: AUTH_MESSAGES.FORBIDDEN,
          });
        }
      } catch (err) {
        return reply.status(401).send({
          success: false,
          error: AUTH_MESSAGES.TOKEN_INVALID,
        });
      }
    };
  }
}

/**
 * Convenience functions for common middleware combinations
 */
export const authMiddleware = {
  // Basic authentication
  auth: AuthMiddleware.requireAuth(),
  activeUser: AuthMiddleware.requireActiveUser(),
  optional: AuthMiddleware.optionalAuth(),

  // Role-based
  admin: AuthMiddleware.adminOnly(),
  brand: AuthMiddleware.brandOrAdmin(),
  influencer: AuthMiddleware.influencerOrAdmin(),
  agency: AuthMiddleware.agencyOrAdmin(),

  // Custom role combinations
  roles: (roles: string[]) => AuthMiddleware.requireRoles(roles),
  selfOrAdmin: (param?: string) => AuthMiddleware.selfOrAdmin(param),
};
