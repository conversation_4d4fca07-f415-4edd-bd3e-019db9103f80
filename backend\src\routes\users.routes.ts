import { FastifyInstance } from 'fastify';
import { UsersController } from '@/controllers/users.controller';
import {
  getUsersSchema,
  getUserByIdSchema,
  createUserSchema,
  updateUserSchema,
  deleteUserSchema,
} from '@/validations/users-route.validation';
import { requireAdmin, requireSelfOrAdmin } from '@/middleware/better-auth.middleware';

export default async function usersRoutes(fastify: FastifyInstance) {
  // GET /users - Get all users with pagination and filtering (Admin only)
  fastify.get(
    '/',
    {
      schema: getUsersSchema,
      onRequest: [requireAdmin],
    },
    UsersController.getUsers
  );

  // GET /users/:id - Get user by ID (Self or Admin)
  fastify.get(
    '/:id',
    {
      schema: getUserByIdSchema,
      onRequest: [requireSelfOrAdmin],
    },
    UsersController.getUserById
  );

  // POST /users - Create a new user (Admin only - for manual user creation)
  fastify.post(
    '/',
    {
      schema: createUserSchema,
      onRequest: [requireAdmin],
    },
    UsersController.createUser
  );

  // PUT /users/:id - Update user by ID (Self or Admin)
  fastify.put(
    '/:id',
    {
      schema: updateUserSchema,
      onRequest: [requireSelfOrAdmin],
    },
    UsersController.updateUser
  );

  // DELETE /users/:id - Delete user by ID (Self or Admin)
  fastify.delete(
    '/:id',
    {
      schema: deleteUserSchema,
      onRequest: [requireSelfOrAdmin],
    },
    UsersController.deleteUser
  );
}
