import 'dotenv/config';
import fastify from 'fastify';
import { validateEnv } from '@/config/env';
import swaggerPlugin from '@/plugins/swagger';
import securityPlugin from '@/plugins/security';
import betterAuthPlugin from '@/plugins/better-auth.plugin';
import errorHandler from '@/middleware/error.middleware';
import routes from '@/routes/index.route';
import { logger } from './config/logger';

const env = validateEnv();

const server = fastify({
  logger,
  trustProxy: true,
});

async function buildServer() {
  try {
    // Register plugins in order - Better Auth first to avoid CORS conflicts
    await server.register(betterAuthPlugin);
    await server.register(securityPlugin);
    await server.register(swaggerPlugin);
    await server.register(errorHandler);

    // Register all routes with API versioning
    await server.register(routes, { prefix: '/api/v1' });

    // Legacy ping route for backward compatibility
    server.get('/ping', async () => ({ message: 'pong' }));

    return server;
  } catch (error) {
    server.log.error(error, 'Error building server');
    process.exit(1);
  }
}

async function start() {
  try {
    const app = await buildServer();

    await app.listen({
      port: env.PORT,
      host: env.HOST,
    });

    server.log.info(`🚀 Server running on http://${env.HOST}:${env.PORT}`);
    server.log.info(
      `📚 API Documentation: http://${env.HOST}:${env.PORT}/docs`
    );
  } catch (error) {
    server.log.error(error, 'Error starting server');
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  server.log.info('SIGTERM received, shutting down gracefully');
  await server.close();
  process.exit(0);
});

process.on('SIGINT', async () => {
  server.log.info('SIGINT received, shutting down gracefully');
  await server.close();
  process.exit(0);
});

if (require.main === module) {
  start();
}

export { buildServer };
