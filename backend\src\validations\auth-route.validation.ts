// JSON Schema definitions for Authentication routes - Body and Query validation only
import { VALIDATION_RULES } from '@/constants';

// POST /auth/register - Register with email/password
export const registerSchema = {
  body: {
    type: 'object',
    properties: {
      email: { type: 'string', format: 'email' },
      name: { type: 'string', minLength: 1, maxLength: 100 },
      password: {
        type: 'string',
        minLength: VALIDATION_RULES.PASSWORD_MIN_LENGTH,
      },
      role: {
        type: 'string',
        enum: ['user', 'influencer', 'brand', 'agency', 'admin'],
      },
    },
    required: ['email', 'name', 'password'],
  },
};

// POST /auth/login - Login with email/password
export const loginSchema = {
  body: {
    type: 'object',
    properties: {
      email: { type: 'string', format: 'email' },
      password: {
        type: 'string',
        minLength: 1,
      },
    },
    required: ['email', 'password'],
  },
};

// GET /auth/google/callback - Google OAuth callback
export const googleCallbackSchema = {
  querystring: {
    type: 'object',
    properties: {
      code: { type: 'string' },
      state: { type: 'string' },
      error: { type: 'string' },
      error_description: { type: 'string' },
    },
    required: ['code'],
  },
};

// No validation needed for these routes as they don't have request bodies/params:
// - GET /auth/google (no body/params)
// - POST /auth/logout (no body/params)
// - GET /auth/me (no body/params, uses JWT from header/cookie)
// - POST /auth/refresh (no body/params, uses JWT from header/cookie)
