{"name": "influencer-marketing", "version": "1.0.0", "description": "Enterprise-grade influencer marketing platform built with Fastify", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "type-check": "tsc --noEmit", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "keywords": ["fastify", "typescript", "influencer", "marketing", "api"], "author": "", "license": "ISC", "dependencies": {"@fastify/basic-auth": "^6.2.0", "@fastify/cors": "^10.0.1", "@fastify/env": "^5.0.1", "@fastify/helmet": "^12.0.1", "@fastify/jwt": "^9.0.1", "@fastify/oauth2": "^8.1.2", "@fastify/rate-limit": "^10.1.1", "@fastify/swagger": "^9.3.0", "@fastify/swagger-ui": "^5.0.1", "@types/bcrypt": "^5.0.2", "bcrypt": "^6.0.0", "better-auth": "^1.2.12", "dotenv": "^17.0.1", "drizzle-orm": "^0.44.2", "fastify": "^5.4.0", "pino": "^9.5.0", "pino-pretty": "^13.0.0", "postgres": "^3.4.5", "zod": "^3.23.8"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^24.0.10", "@typescript-eslint/eslint-plugin": "^8.18.2", "@typescript-eslint/parser": "^8.18.2", "drizzle-kit": "^0.31.4", "eslint": "^9.18.0", "eslint-config-prettier": "^9.1.0", "jest": "^29.7.0", "prettier": "^3.4.2", "ts-jest": "^29.2.5", "tsx": "^4.19.2", "typescript": "^5.8.3"}}