import { FastifyInstance } from 'fastify';
import { AuthController } from '@/controllers/auth.controller';
import {
  registerSchema,
  loginSchema,
  googleCallbackSchema,
} from '@/validations/auth-route.validation';
import { authMiddleware } from '@/middleware/auth.middleware';

export default async function authRoutes(fastify: FastifyInstance) {
  // POST /auth/register - Register with email/password (Public route)
  fastify.post(
    '/register',
    {
      schema: registerSchema,
    },
    AuthController.register
  );

  // POST /auth/login - Login with email/password (Public route)
  fastify.post(
    '/login',
    {
      schema: loginSchema,
    },
    AuthController.login
  );

  // Note: /auth/google route is automatically created by @fastify/oauth2 plugin
  // It redirects to Google OAuth authorization URL

  // GET /auth/google/callback - Handle Google OAuth callback (Public route)
  fastify.get(
    '/google/callback',
    {
      schema: googleCallbackSchema,
    },
    AuthController.googleCallback
  );

  // POST /auth/logout - Logout user (Public route, but can be used by authenticated users)
  fastify.post('/logout', AuthController.logout);

  // GET /auth/me - Get current user info (Protected route)
  fastify.get(
    '/me',
    {
      onRequest: [authMiddleware.activeUser],
    },
    AuthController.getCurrentUser
  );

  // POST /auth/refresh - Refresh JWT token (Protected route)
  fastify.post(
    '/refresh',
    {
      onRequest: [authMiddleware.activeUser],
    },
    AuthController.refreshToken
  );
}
