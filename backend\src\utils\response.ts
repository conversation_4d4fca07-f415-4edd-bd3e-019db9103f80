import { FastifyReply } from 'fastify';
import { ApiResponse, PaginatedResponse } from '@/types';
import { HTTP_STATUS, HTTP_MESSAGES } from '@/constants';

// Common success response handler
export const sendSuccess = <T>(
  reply: FastifyReply,
  data?: T,
  message: string = HTTP_MESSAGES.SUCCESS,
  statusCode: number = HTTP_STATUS.OK
): void => {
  reply.status(statusCode).send({
    success: true,
    data,
    message,
  } as ApiResponse<T>);
};

// Common error response handler
export const sendError = (
  reply: FastifyReply,
  error: string,
  message: string = HTTP_MESSAGES.BAD_REQUEST,
  statusCode: number = HTTP_STATUS.BAD_REQUEST
): void => {
  reply.status(statusCode).send({
    success: false,
    error,
    message,
  } as ApiResponse);
};

// Paginated response handler
export const sendPaginated = <T>(
  reply: FastifyReply,
  data: T[],
  pagination: PaginatedResponse<T>['pagination'],
  message: string = HTTP_MESSAGES.SUCCESS
): void => {
  reply.status(HTTP_STATUS.OK).send({
    success: true,
    data,
    message,
    pagination,
  } as PaginatedResponse<T>);
};

// Specific success response helpers for common HTTP status codes
export const sendCreated = <T>(
  reply: FastifyReply,
  data: T,
  message: string = HTTP_MESSAGES.CREATED
): void => {
  sendSuccess(reply, data, message, HTTP_STATUS.CREATED);
};

export const sendUpdated = <T>(
  reply: FastifyReply,
  data: T,
  message: string = HTTP_MESSAGES.UPDATED
): void => {
  sendSuccess(reply, data, message, HTTP_STATUS.OK);
};

export const sendDeleted = (
  reply: FastifyReply,
  message: string = HTTP_MESSAGES.DELETED
): void => {
  sendSuccess(reply, { deleted: true }, message, HTTP_STATUS.OK);
};

// Specific error response helpers for common HTTP status codes
export const sendBadRequest = (
  reply: FastifyReply,
  error: string = HTTP_MESSAGES.BAD_REQUEST,
  message: string = HTTP_MESSAGES.VALIDATION_ERROR
): void => {
  sendError(reply, error, message, HTTP_STATUS.BAD_REQUEST);
};

export const sendUnauthorized = (
  reply: FastifyReply,
  error: string = HTTP_MESSAGES.UNAUTHORIZED,
  message: string = HTTP_MESSAGES.UNAUTHORIZED
): void => {
  sendError(reply, error, message, HTTP_STATUS.UNAUTHORIZED);
};

export const sendForbidden = (
  reply: FastifyReply,
  error: string = HTTP_MESSAGES.FORBIDDEN,
  message: string = HTTP_MESSAGES.FORBIDDEN
): void => {
  sendError(reply, error, message, HTTP_STATUS.FORBIDDEN);
};

export const sendNotFound = (
  reply: FastifyReply,
  error: string = HTTP_MESSAGES.NOT_FOUND,
  message: string = HTTP_MESSAGES.NOT_FOUND
): void => {
  sendError(reply, error, message, HTTP_STATUS.NOT_FOUND);
};

export const sendConflict = (
  reply: FastifyReply,
  error: string = 'Conflict',
  message: string = 'Resource already exists'
): void => {
  sendError(reply, error, message, HTTP_STATUS.CONFLICT);
};

export const sendInternalError = (
  reply: FastifyReply,
  error: string = HTTP_MESSAGES.INTERNAL_ERROR,
  message: string = HTTP_MESSAGES.INTERNAL_ERROR
): void => {
  sendError(reply, error, message, HTTP_STATUS.INTERNAL_SERVER_ERROR);
};
