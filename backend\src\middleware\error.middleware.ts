import { FastifyInstance, FastifyError } from 'fastify';

export default async function errorHandler(fastify: FastifyInstance) {
  fastify.setErrorHandler((error: FastifyError, request, reply) => {
    request.log.error(
      {
        error: error.message,
        stack: error.stack,
        url: request.url,
        method: request.method,
      },
      'Request error'
    );

    const statusCode = error.statusCode || 500;
    const message =
      statusCode === 500 ? 'Internal Server Error' : error.message;

    reply.status(statusCode).send({
      success: false,
      error: message,
      ...(process.env['NODE_ENV'] === 'development' && { stack: error.stack }),
    });
  });

  fastify.setNotFoundHandler((_request, reply) => {
    reply.status(404).send({
      success: false,
      error: 'Route not found',
    });
  });
}
