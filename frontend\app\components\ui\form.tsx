import * as React from "react";
import { useFormContext, Controller } from "react-hook-form";

export function Form({ children, ...props }: React.FormHTMLAttributes<HTMLFormElement>) {
  return (
    <form {...props}>{children}</form>
  );
}

export function FormField({ name, render }: { name: string; render: (field: any) => React.ReactNode }) {
  const { control } = useFormContext();
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState }) => render({ ...field, error: fieldState.error })}
    />
  );
}

export function FormItem({ children, className = "mb-4" }: { children: React.ReactNode; className?: string }) {
  return <div className={className}>{children}</div>;
}

export function FormLabel({ children, htmlFor }: { children: React.ReactNode; htmlFor?: string }) {
  return (
    <label htmlFor={htmlFor} className="block text-sm font-medium text-gray-700 mb-1">
      {children}
    </label>
  );
}

export function FormControl({ children }: { children: React.ReactNode }) {
  return <div>{children}</div>;
}

export function FormDescription({ children }: { children: React.ReactNode }) {
  return <p className="text-xs text-gray-500 mt-1">{children}</p>;
}

export function FormMessage({ children }: { children?: React.ReactNode }) {
  return children ? <p className="text-xs text-red-500 mt-1">{children}</p> : null;
}
