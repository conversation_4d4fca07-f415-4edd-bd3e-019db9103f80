import { use<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { signUp } from "~/lib/auth-client";
import { useNavigate } from "react-router";
import type { Route } from "./+types/register";
import { registerSchema, type RegisterSchema } from "~/validations/register";
import {
	Form,
	FormField,
	FormItem,
	FormLabel,
	FormControl,
	FormMessage,
	FormDescription,
} from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { Button } from "~/components/ui/button";

export function meta({}: Route.MetaArgs) {
	return [
		{ title: "Register - Influencer Marketing" },
		{ name: "description", content: "Create your account" },
	];
}

export default function Register() {
	const navigate = useNavigate();
	const methods = useForm<RegisterSchema>({
		resolver: zodResolver(registerSchema),
	});

	const {
		handleSubmit,
		formState: { isSubmitting },
	} = methods;

	const onSubmit = async (data: RegisterSchema) => {
		try {
			await signUp.email({
				email: data.email,
				password: data.password,
				name: data.name,
			});
			navigate("/");
		} catch (error) {
			console.error("Registration failed:", error);
		}
	};

	return (
		<div className="min-h-screen flex items-center justify-center bg-gray-50">
			<div className="max-w-md w-full space-y-8">
				<div>
					<h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
						Create your account
					</h2>
				</div>
				<FormProvider {...methods}>
					<Form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)} noValidate>
						<FormField
							name="name"
							render={({ value, onChange, onBlur, ref, error }) => (
								<FormItem>
									<FormLabel htmlFor="name">Full Name</FormLabel>
									<FormControl>
										<Input
											id="name"
											type="text"
											autoComplete="name"
											placeholder="Full Name"
											value={value}
											onChange={onChange}
											onBlur={onBlur}
											ref={ref}
										/>
									</FormControl>
									<FormMessage>{error?.message}</FormMessage>
								</FormItem>
							)}
						/>
						<FormField
							name="email"
							render={({ value, onChange, onBlur, ref, error }) => (
								<FormItem>
									<FormLabel htmlFor="email">Email address</FormLabel>
									<FormControl>
										<Input
											id="email"
											type="email"
											autoComplete="email"
											placeholder="Email address"
											value={value}
											onChange={onChange}
											onBlur={onBlur}
											ref={ref}
										/>
									</FormControl>
									<FormMessage>{error?.message}</FormMessage>
								</FormItem>
							)}
						/>
						<FormField
							name="password"
							render={({ value, onChange, onBlur, ref, error }) => (
								<FormItem>
									<FormLabel htmlFor="password">Password</FormLabel>
									<FormControl>
										<Input
											id="password"
											type="password"
											autoComplete="new-password"
											placeholder="Password"
											value={value}
											onChange={onChange}
											onBlur={onBlur}
											ref={ref}
										/>
									</FormControl>
									<FormMessage>{error?.message}</FormMessage>
								</FormItem>
							)}
						/>
						<div>
							<Button
								type="submit"
								className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
								disabled={isSubmitting}
							>
								{isSubmitting ? "Creating account..." : "Create account"}
							</Button>
						</div>
					</Form>
				</FormProvider>
			</div>
		</div>
	);
}
