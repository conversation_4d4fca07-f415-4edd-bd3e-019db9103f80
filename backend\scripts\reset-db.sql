-- Drop all tables and types to start fresh
DROP TABLE IF EXISTS "verification" CASCADE;
DROP TABLE IF EXISTS "session" CASCADE;
DROP TABLE IF EXISTS "account" CASCADE;
DROP TABLE IF EXISTS "predictions" CASCADE;
DROP TABLE IF EXISTS "subscriptions" CASCADE;
DROP TABLE IF EXISTS "transactions" CASCADE;
DROP TABLE IF EXISTS "applications" CASCADE;
DROP TABLE IF EXISTS "campaigns" CASCADE;
DROP TABLE IF EXISTS "brand_profiles" CASCADE;
DROP TABLE IF EXISTS "influencer_profiles" CASCADE;
DROP TABLE IF EXISTS "posts" CASCADE;
DROP TABLE IF EXISTS "social_media_accounts" CASCADE;
DROP TABLE IF EXISTS "user_languages" CASCADE;
DROP TABLE IF EXISTS "languages" CASCADE;
DROP TABLE IF EXISTS "users" CASCADE;

-- Drop all enums
DROP TYPE IF EXISTS "user_roles" CASCADE;
DROP TYPE IF EXISTS "subscription_tiers" CASCADE;
DROP TYPE IF EXISTS "platforms" CASCADE;
DROP TYPE IF EXISTS "post_formats" CASCADE;
DROP TYPE IF EXISTS "campaign_statuses" CASCADE;
DROP TYPE IF EXISTS "application_statuses" CASCADE;
DROP TYPE IF EXISTS "transaction_types" CASCADE;
DROP TYPE IF EXISTS "transaction_statuses" CASCADE;