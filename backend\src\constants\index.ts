// HTTP Status Messages
export const HTTP_MESSAGES = {
  SUCCESS: 'Request successful',
  CREATED: 'Resource created successfully',
  UPDATED: 'Resource updated successfully',
  DELETED: 'Resource deleted successfully',
  NOT_FOUND: 'Resource not found',
  UNAUTHORIZED: 'Unauthorized access',
  FORBIDDEN: 'Access forbidden',
  VALIDATION_ERROR: 'Validation failed',
  INTERNAL_ERROR: 'Internal server error',
  BAD_REQUEST: 'Bad request',
} as const;

// User Messages
export const USER_MESSAGES = {
  CREATED: 'User created successfully',
  UPDATED: 'User updated successfully',
  DELETED: 'User deleted successfully',
  NOT_FOUND: 'User not found',
  EMAIL_EXISTS: 'Email already exists',
  INVALID_CREDENTIALS: 'Invalid email or password',
  PROFILE_UPDATED: 'Profile updated successfully',
} as const;

// Auth Messages
export const AUTH_MESSAGES = {
  LOGIN_SUCCESS: 'Login successful',
  LOGOUT_SUCCESS: 'Logout successful',
  REGISTER_SUCCESS: 'Registration successful',
  TOKEN_INVALID: 'Invalid or expired token',
  TOKEN_REQUIRED: 'Authentication token required',
  TOKEN_EXPIRED: 'Token has expired',
  PASSWORD_CHANGED: 'Password changed successfully',
  PASSWORD_RESET_SENT: 'Password reset link sent',
  INVALID_CREDENTIALS: 'Invalid email or password',
  EMAIL_ALREADY_EXISTS: 'Email already exists',
  GOOGLE_AUTH_SUCCESS: 'Google authentication successful',
  GOOGLE_AUTH_FAILED: 'Google authentication failed',
  UNAUTHORIZED: 'Unauthorized access',
  FORBIDDEN: 'Access forbidden',
  ACCOUNT_INACTIVE: 'Account is inactive',
} as const;

// Campaign Messages
export const CAMPAIGN_MESSAGES = {
  CREATED: 'Campaign created successfully',
  UPDATED: 'Campaign updated successfully',
  DELETED: 'Campaign deleted successfully',
  NOT_FOUND: 'Campaign not found',
  STATUS_UPDATED: 'Campaign status updated',
} as const;

// Influencer Messages
export const INFLUENCER_MESSAGES = {
  PROFILE_CREATED: 'Influencer profile created successfully',
  PROFILE_UPDATED: 'Influencer profile updated successfully',
  NOT_FOUND: 'Influencer not found',
  VERIFICATION_PENDING: 'Verification request submitted',
  VERIFIED: 'Influencer verified successfully',
} as const;

// Brand Messages
export const BRAND_MESSAGES = {
  PROFILE_CREATED: 'Brand profile created successfully',
  PROFILE_UPDATED: 'Brand profile updated successfully',
  NOT_FOUND: 'Brand not found',
} as const;

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// User Roles
export const USER_ROLES = {
  ADMIN: 'admin',
  BRAND: 'brand',
  INFLUENCER: 'influencer',
  AGENCY: 'agency',
  USER: 'user',
} as const;

// Authentication Methods
export const AUTH_METHODS = {
  EMAIL_PASSWORD: 'email_password',
  GOOGLE: 'google',
} as const;

// Campaign Status
export const CAMPAIGN_STATUS = {
  DRAFT: 'draft',
  ACTIVE: 'active',
  PAUSED: 'paused',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
} as const;

// Pagination Defaults
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 10,
  MAX_LIMIT: 100,
} as const;

// Validation Rules
export const VALIDATION_RULES = {
  PASSWORD_MIN_LENGTH: 8,
  EMAIL_MAX_LENGTH: 255,
  NAME_MAX_LENGTH: 100,
  TITLE_MAX_LENGTH: 255,
  BIO_MAX_LENGTH: 1000,
} as const;
