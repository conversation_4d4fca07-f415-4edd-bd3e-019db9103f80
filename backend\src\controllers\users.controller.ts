import { FastifyRequest, FastifyReply } from 'fastify';
import {
  sendSuccess,
  sendCreated,
  sendUpdated,
  sendDeleted,
  sendPaginated,
  sendInternalError,
  sendNotFound,
  sendBadRequest,
} from '@/utils/response';
import { USER_MESSAGES, PAGINATION } from '@/constants';
import { UserService } from '@/services/user.service';

// Types for request parameters and body
interface GetUsersQuery {
  page?: number;
  limit?: number;
  search?: string;
  isActive?: boolean;
}

interface UserParams {
  id: string;
}

interface CreateUserBody {
  email: string;
  name: string;
  password: string;
  role?: 'user' | 'influencer' | 'brand' | 'agency' | 'admin';
}

interface UpdateUserBody {
  email?: string;
  name?: string;
  isActive?: boolean;
  password?: string;
  role?: 'user' | 'influencer' | 'brand' | 'agency' | 'admin';
}

export class UsersController {
  // GET /users - Get all users with pagination and filtering
  static async getUsers(
    request: FastifyRequest<{ Querystring: GetUsersQuery }>,
    reply: FastifyReply
  ) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        search,
      } = request.query;

      const result = await UserService.getAllUsers(page, limit, search);
      const { users: paginatedUsers, total, totalPages } = result;
      return sendPaginated(
        reply,
        paginatedUsers,
        {
          page,
          limit,
          total,
          totalPages,
        },
        'Users retrieved successfully'
      );
    } catch (error) {
      request.log.error(error, 'Error getting users');
      return sendInternalError(reply, 'Failed to retrieve users');
    }
  }

  // GET /users/:id - Get user by ID
  static async getUserById(
    request: FastifyRequest<{ Params: UserParams }>,
    reply: FastifyReply
  ) {
    try {
      const { id } = request.params;

      const user = await UserService.getUserById(id);
      if (!user) {
        return sendNotFound(reply, USER_MESSAGES.NOT_FOUND);
      }

      return sendSuccess(reply, user, 'User retrieved successfully');
    } catch (error) {
      request.log.error(error, 'Error getting user by ID');
      return sendInternalError(reply, 'Failed to retrieve user');
    }
  }

  // POST /users - Create a new user
  static async createUser(
    request: FastifyRequest<{ Body: CreateUserBody }>,
    reply: FastifyReply
  ) {
    try {
      const userData = request.body;

      const newUser = await UserService.createUserWithPassword({
        email: userData.email,
        name: userData.name,
        password: userData.password,
        role: userData.role,
      });

      return sendCreated(reply, newUser, USER_MESSAGES.CREATED);
    } catch (error) {
      request.log.error(error, 'Error creating user');
      if (error instanceof Error && error.message.includes('already exists')) {
        return sendBadRequest(reply, error.message);
      }
      return sendInternalError(reply, 'Failed to create user');
    }
  }

  // PUT /users/:id - Update user by ID
  static async updateUser(
    request: FastifyRequest<{ Params: UserParams; Body: UpdateUserBody }>,
    reply: FastifyReply
  ) {
    try {
      const { id } = request.params;
      const updateData = request.body;

      // If password is being updated, it should be in passwordHash field for the service
      const serviceUpdateData = updateData.password
        ? {
            ...updateData,
            passwordHash: updateData.password,
            password: undefined,
          }
        : updateData;

      const updatedUser = await UserService.updateUser(id, serviceUpdateData);
      if (!updatedUser) {
        return sendNotFound(reply, USER_MESSAGES.NOT_FOUND);
      }

      return sendUpdated(reply, updatedUser, USER_MESSAGES.UPDATED);
    } catch (error) {
      request.log.error(error, 'Error updating user');
      return sendInternalError(reply, 'Failed to update user');
    }
  }

  // DELETE /users/:id - Delete user by ID
  static async deleteUser(
    request: FastifyRequest<{ Params: UserParams }>,
    reply: FastifyReply
  ) {
    try {
      const { id } = request.params;
      const deletedUser = await UserService.deleteUser(id);
      if (!deletedUser || deletedUser.length === 0) {
        return sendNotFound(reply, USER_MESSAGES.NOT_FOUND);
      }
      return sendDeleted(reply, USER_MESSAGES.DELETED);
    } catch (error) {
      request.log.error(error, 'Error deleting user');
      return sendInternalError(reply, 'Failed to delete user');
    }
  }
}
