# Influencer Marketing Platform

Enterprise-grade influencer marketing platform built with Fastify and TypeScript.

## 🚀 Features

- **Enterprise Architecture**: Layered architecture with controllers, services, and repositories
- **Type Safety**: Full TypeScript support with strict configuration
- **Security**: Helmet, CORS, rate limiting, and JWT authentication
- **Documentation**: Auto-generated OpenAPI/Swagger documentation
- **Testing**: Jest with unit and integration tests
- **Logging**: Structured logging with Pino
- **Docker**: Multi-stage Dockerfile and Docker Compose
- **Code Quality**: ESLint, Prettier, and pre-commit hooks

## 📁 Project Structure

```
src/
├── config/          # Configuration files
├── controllers/     # Request handlers
├── services/        # Business logic
├── repositories/    # Data access layer
├── models/          # Data models
├── plugins/         # Fastify plugins
├── routes/          # Route definitions
├── middleware/      # Custom middleware
├── utils/           # Utility functions
└── types/           # TypeScript type definitions

tests/
├── unit/            # Unit tests
└── integration/     # Integration tests
```

## 🛠️ Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Docker (optional)

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Copy environment variables:
   ```bash
   copy .env.example .env
   ```

4. Start development server:
   ```bash
   npm run dev
   ```

### Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm test` - Run tests
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage
- `npm run lint` - Lint code
- `npm run format` - Format code with Prettier

### Docker Development

```bash
docker-compose up
```

## 📚 API Documentation

Once the server is running, visit:
- API Documentation: http://localhost:8080/docs
- Health Check: http://localhost:8080/api/v1/health

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Generate coverage report
npm run test:coverage
```

## 🔧 Environment Variables

See `.env.example` for all available environment variables.

## 📦 Deployment

### Production Build

```bash
npm run build
npm start
```

### Docker Production

```bash
docker build -t influencer-marketing .
docker run -p 8080:8080 influencer-marketing
```

## 🤝 Contributing

1. Follow the existing code style
2. Write tests for new features
3. Update documentation as needed
4. Run linting and tests before committing