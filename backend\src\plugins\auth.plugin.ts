import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import fastifyJwt from '@fastify/jwt';
import fastifyOAuth2 from '@fastify/oauth2';
import { validateEnv } from '@/config/env';
import { AUTH_MESSAGES } from '@/constants';

const env = validateEnv();

// Define user type
export interface AuthUser {
  id: string;
  email: string;
  name: string;
  role: string;
  isActive: boolean;
}

// Extend FastifyRequest to include user property
declare module '@fastify/jwt' {
  interface FastifyJWT {
    user: AuthUser;
  }
}

export default async function authPlugin(fastify: FastifyInstance) {
  // Register JWT plugin
  await fastify.register(fastifyJwt, {
    secret: env.JWT_SECRET,
    sign: {
      expiresIn: env.JWT_EXPIRES_IN,
    },
    cookie: {
      cookieName: 'token',
      signed: false,
    },
  });

  // Register Google OAuth2 plugin
  await fastify.register(fastifyOAuth2, {
    name: 'googleOAuth2',
    scope: ['profile', 'email'],
    credentials: {
      client: {
        id: env.GOOGLE_CLIENT_ID,
        secret: env.GOOGLE_CLIENT_SECRET,
      },
      auth: fastifyOAuth2.GOOGLE_CONFIGURATION,
    },
    startRedirectPath: '/api/v1/auth/google',
    callbackUri: env.GOOGLE_REDIRECT_URI,
  });

  // JWT Authentication decorator
  fastify.decorate(
    'authenticate',
    async function (request: FastifyRequest, reply: FastifyReply) {
      try {
        await request.jwtVerify();
      } catch (err) {
        reply.status(401).send({
          success: false,
          error: AUTH_MESSAGES.TOKEN_INVALID,
        });
      }
    }
  );

  // Optional JWT Authentication decorator (doesn't throw error if no token)
  fastify.decorate(
    'optionalAuthenticate',
    async function (request: FastifyRequest, _reply: FastifyReply) {
      try {
        await request.jwtVerify();
      } catch (err) {
        // Don't throw error, just continue without user
        (request as any).user = undefined;
      }
    }
  );

  // Role-based authorization decorator
  fastify.decorate('authorize', function (allowedRoles: string[]) {
    return async function (request: FastifyRequest, reply: FastifyReply) {
      try {
        await request.jwtVerify();

        if (!request.user) {
          return reply.status(401).send({
            success: false,
            error: AUTH_MESSAGES.UNAUTHORIZED,
          });
        }

        const authUser = request.user as AuthUser;
        if (!allowedRoles.includes(authUser.role)) {
          return reply.status(403).send({
            success: false,
            error: AUTH_MESSAGES.FORBIDDEN,
          });
        }
      } catch (err) {
        return reply.status(401).send({
          success: false,
          error: AUTH_MESSAGES.TOKEN_INVALID,
        });
      }
    };
  });

  // Check if user is active
  fastify.decorate(
    'requireActiveUser',
    async function (request: FastifyRequest, reply: FastifyReply) {
      if (!request.user) {
        return reply.status(401).send({
          success: false,
          error: AUTH_MESSAGES.UNAUTHORIZED,
        });
      }

      const authUser = request.user as AuthUser;
      if (!authUser.isActive) {
        return reply.status(403).send({
          success: false,
          error: AUTH_MESSAGES.ACCOUNT_INACTIVE,
        });
      }
    }
  );

  // Combined authentication and active user check
  fastify.decorate(
    'authenticateActive',
    async function (request: FastifyRequest, reply: FastifyReply) {
      await fastify.authenticate(request, reply);
      await fastify.requireActiveUser(request, reply);
    }
  );

  // Combined authentication, active user check, and role authorization
  fastify.decorate(
    'authenticateAndAuthorize',
    function (allowedRoles: string[]) {
      return async function (request: FastifyRequest, reply: FastifyReply) {
        await fastify.authenticate(request, reply);
        await fastify.requireActiveUser(request, reply);
        await fastify.authorize(allowedRoles)(request, reply);
      };
    }
  );
}

// Extend Fastify instance to include our custom decorators
declare module 'fastify' {
  interface FastifyInstance {
    authenticate: (
      request: FastifyRequest,
      reply: FastifyReply
    ) => Promise<void>;
    optionalAuthenticate: (
      request: FastifyRequest,
      reply: FastifyReply
    ) => Promise<void>;
    authorize: (
      allowedRoles: string[]
    ) => (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
    requireActiveUser: (
      request: FastifyRequest,
      reply: FastifyReply
    ) => Promise<void>;
    authenticateActive: (
      request: FastifyRequest,
      reply: FastifyReply
    ) => Promise<void>;
    authenticateAndAuthorize: (
      allowedRoles: string[]
    ) => (request: FastifyRequest, reply: FastifyReply) => Promise<void>;
    googleOAuth2: any;
  }
}
