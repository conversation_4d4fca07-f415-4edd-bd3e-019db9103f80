import { FastifyInstance } from 'fastify';
import healthRoutes from './health.routes';
import betterAuthRoutes from './better-auth.routes';
import usersRoutes from './users.routes';

export default async function routes(fastify: FastifyInstance) {
  // Register all route modules with their prefixes

  // Health routes - /api/v1/health (Public)
  await fastify.register(healthRoutes, { prefix: '/health' });

  // Authentication routes - /api/v1/auth (Mixed public/protected)
  await fastify.register(betterAuthRoutes, { prefix: '/auth' });

  // Users routes - /api/v1/users (Protected)
  await fastify.register(usersRoutes, { prefix: '/users' });

  // Add more route modules here as you create them
  // Example:
  // await fastify.register(campaignsRoutes, { prefix: '/campaigns' });
  // await fastify.register(influencersRoutes, { prefix: '/influencers' });
}
