import { and, eq, ilike, or } from 'drizzle-orm';
import { db } from '@/config/database';
import { users } from '@/models/schema';

export class UserRepository {
  static async findById(id: string, includeInactive = false) {
    const conditions = includeInactive
      ? eq(users.id, id)
      : and(eq(users.id, id), eq(users.isActive, true));

    return await db.select().from(users).where(conditions).limit(1);
  }

  static async findAll(page: number, limit: number, search?: string) {
    const searchCondition = search
      ? or(ilike(users.email, `%${search}%`), ilike(users.name, `%${search}%`))
      : undefined;

    const activeCondition = eq(users.isActive, true);

    const query = db
      .select()
      .from(users)
      .where(and(searchCondition, activeCondition));

    const total = (await query).length;
    const totalPages = Math.ceil(total / limit);
    const startIndex = (page - 1) * limit;
    const paginatedUsers = await query.offset(startIndex).limit(limit);
    return { users: paginatedUsers, total, totalPages };
  }

  static async findByEmail(email: string, includeInactive = false) {
    const conditions = includeInactive
      ? eq(users.email, email)
      : and(eq(users.email, email), eq(users.isActive, true));

    return await db.select().from(users).where(conditions).limit(1);
  }

  static async findByGoogleId(googleId: string, includeInactive = false) {
    const conditions = includeInactive
      ? eq(users.googleId, googleId)
      : and(eq(users.googleId, googleId), eq(users.isActive, true));

    return await db.select().from(users).where(conditions).limit(1);
  }

  static async create(userData: typeof users.$inferInsert) {
    return await db.insert(users).values(userData).returning();
  }

  static async update(
    id: string,
    userData: Partial<typeof users.$inferInsert>
  ) {
    return await db
      .update(users)
      .set(userData)
      .where(and(eq(users.id, id), eq(users.isActive, true)))
      .returning();
  }
  static async delete(id: string) {
    return await db
      .update(users)
      .set({ isActive: false })
      .where(and(eq(users.id, id), eq(users.isActive, true)))
      .returning();
  }
}
