import { validateEnv } from './env';

const env = validateEnv();

export const logger =
  env.NODE_ENV === 'development'
    ? {
        transport: {
          target: 'pino-pretty',
          options: {
            colorize: true,
            translateTime: 'SYS:standard',
            ignore: 'pid,hostname',
          },
        },
        level: env.LOG_LEVEL,
      }
    : {
        level: env.LOG_LEVEL,
      };
