import { FastifyInstance } from 'fastify';
import { HealthController } from '@/controllers/health.controller';

export default async function healthRoutes(fastify: FastifyInstance) {
  fastify.get('/health', {
    schema: {
      tags: ['Health'],
      summary: 'Health check endpoint',
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                status: { type: 'string' },
                timestamp: { type: 'string' },
                uptime: { type: 'number' },
                environment: { type: 'string' },
                version: { type: 'string' },
              },
            },
            message: { type: 'string' },
          },
        },
      },
    },
  }, HealthController.getHealth);

  fastify.get('/ready', {
    schema: {
      tags: ['Health'],
      summary: 'Readiness check endpoint',
    },
  }, HealthController.getReadiness);
}