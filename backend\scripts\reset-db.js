import postgres from 'postgres';
import 'dotenv/config';

const sql = postgres(process.env.DATABASE_URL);

async function resetDatabase() {
  try {
    console.log('Resetting database...');
    
    // Drop all tables and types
    await sql`DROP TABLE IF EXISTS "verification" CASCADE`;
    await sql`DROP TABLE IF EXISTS "session" CASCADE`;
    await sql`DROP TABLE IF EXISTS "account" CASCADE`;
    await sql`DROP TABLE IF EXISTS "predictions" CASCADE`;
    await sql`DROP TABLE IF EXISTS "subscriptions" CASCADE`;
    await sql`DROP TABLE IF EXISTS "transactions" CASCADE`;
    await sql`DROP TABLE IF EXISTS "applications" CASCADE`;
    await sql`DROP TABLE IF EXISTS "campaigns" CASCADE`;
    await sql`DROP TABLE IF EXISTS "brand_profiles" CASCADE`;
    await sql`DROP TABLE IF EXISTS "influencer_profiles" CASCADE`;
    await sql`DROP TABLE IF EXISTS "posts" CASCADE`;
    await sql`DROP TABLE IF EXISTS "social_media_accounts" CASCADE`;
    await sql`DROP TABLE IF EXISTS "user_languages" CASCADE`;
    await sql`DROP TABLE IF EXISTS "languages" CASCADE`;
    await sql`DROP TABLE IF EXISTS "users" CASCADE`;
    
    // Drop all enums
    await sql`DROP TYPE IF EXISTS "user_roles" CASCADE`;
    await sql`DROP TYPE IF EXISTS "subscription_tiers" CASCADE`;
    await sql`DROP TYPE IF EXISTS "platforms" CASCADE`;
    await sql`DROP TYPE IF EXISTS "post_formats" CASCADE`;
    await sql`DROP TYPE IF EXISTS "campaign_statuses" CASCADE`;
    await sql`DROP TYPE IF EXISTS "application_statuses" CASCADE`;
    await sql`DROP TYPE IF EXISTS "transaction_types" CASCADE`;
    await sql`DROP TYPE IF EXISTS "transaction_statuses" CASCADE`;
    
    console.log('Database reset complete!');
    process.exit(0);
  } catch (error) {
    console.error('Error resetting database:', error);
    process.exit(1);
  }
}

resetDatabase();