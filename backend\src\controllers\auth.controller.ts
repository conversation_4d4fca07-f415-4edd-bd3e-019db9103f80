import { FastifyRequest, FastifyReply } from 'fastify';
import {
  sendSuc<PERSON>,
  sendCreated,
  sendBadRequest,
  sendInternalError,
  sendUnauthorized,
} from '@/utils/response';
import { AUTH_MESSAGES } from '@/constants';
import { UserService } from '@/services/user.service';
import { validateEnv } from '@/config/env';
import { AuthUser } from '@/plugins/auth.plugin';

const env = validateEnv();

// Types for request bodies
interface LoginBody {
  email: string;
  password: string;
}

interface RegisterBody {
  email: string;
  name: string;
  password: string;
  role?: 'user' | 'influencer' | 'brand' | 'agency' | 'admin';
}

interface GoogleCallbackQuery {
  code: string;
  state?: string;
}

export class AuthController {
  // POST /auth/register - Register with email/password
  static async register(
    request: FastifyRequest<{ Body: RegisterBody }>,
    reply: FastifyReply
  ) {
    try {
      const { email, name, password, role } = request.body;

      const newUser = await UserService.createUserWithPassword({
        email,
        name,
        password,
        role: role || 'user',
      });

      const user = newUser[0];
      if (!user) {
        throw new Error('Failed to create user');
      }

      // Generate JWT token
      const token = request.server.jwt.sign({
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        isActive: user.isActive,
      });

      // Set cookie
      reply.setCookie('token', token, {
        httpOnly: true,
        secure: env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
      });

      return sendCreated(
        reply,
        {
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role,
            isActive: user.isActive,
          },
          token,
        },
        AUTH_MESSAGES.REGISTER_SUCCESS
      );
    } catch (error) {
      request.log.error(error, 'Error during registration');
      if (error instanceof Error && error.message.includes('already exists')) {
        return sendBadRequest(reply, error.message);
      }
      return sendInternalError(reply, 'Registration failed');
    }
  }

  // POST /auth/login - Login with email/password
  static async login(
    request: FastifyRequest<{ Body: LoginBody }>,
    reply: FastifyReply
  ) {
    try {
      const { email, password } = request.body;

      const user = await UserService.authenticateUser(email, password);

      // Generate JWT token
      const token = request.server.jwt.sign({
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        isActive: user.isActive,
      });

      // Set cookie
      reply.setCookie('token', token, {
        httpOnly: true,
        secure: env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
      });

      return sendSuccess(
        reply,
        {
          user: {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role,
            isActive: user.isActive,
          },
          token,
        },
        AUTH_MESSAGES.LOGIN_SUCCESS
      );
    } catch (error) {
      request.log.error(error, 'Error during login');
      if (
        error instanceof Error &&
        error.message === AUTH_MESSAGES.INVALID_CREDENTIALS
      ) {
        return sendUnauthorized(reply, error.message);
      }
      return sendInternalError(reply, 'Login failed');
    }
  }

  // Note: Google OAuth initiation is handled automatically by @fastify/oauth2 plugin
  // The /auth/google route is created automatically and redirects to Google

  // GET /auth/google/callback - Handle Google OAuth callback
  static async googleCallback(
    request: FastifyRequest<{ Querystring: GoogleCallbackQuery }>,
    reply: FastifyReply
  ) {
    try {
      const { token } =
        await request.server.googleOAuth2.getAccessTokenFromAuthorizationCodeFlow(
          request
        );

      // Get user info from Google
      const response = await fetch(
        'https://www.googleapis.com/oauth2/v2/userinfo',
        {
          headers: {
            Authorization: `Bearer ${token.access_token}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error('Failed to fetch user info from Google');
      }

      const googleUser = (await response.json()) as {
        id: string;
        email: string;
        name: string;
      };

      // Create or update user with Google data
      const user = await UserService.createOrUpdateGoogleUser({
        email: googleUser.email,
        name: googleUser.name,
        googleId: googleUser.id,
        role: 'user',
      });

      const userData = Array.isArray(user) ? user[0] : user;
      if (!userData) {
        throw new Error('Failed to create or update user');
      }

      // Generate JWT token
      const jwtToken = request.server.jwt.sign({
        id: userData.id,
        email: userData.email,
        name: userData.name,
        role: userData.role,
        isActive: userData.isActive,
      });

      // Set cookie
      reply.setCookie('token', jwtToken, {
        httpOnly: true,
        secure: env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
      });

      // Redirect to frontend with success
      return reply.redirect(
        `${env.FRONTEND_URL}/auth/success?token=${jwtToken}`
      );
    } catch (error) {
      request.log.error(error, 'Error during Google OAuth callback');
      return reply.redirect(
        `${env.FRONTEND_URL}/auth/error?message=${AUTH_MESSAGES.GOOGLE_AUTH_FAILED}`
      );
    }
  }

  // POST /auth/logout - Logout user
  static async logout(request: FastifyRequest, reply: FastifyReply) {
    try {
      // Clear the cookie
      reply.clearCookie('token');

      return sendSuccess(reply, null, AUTH_MESSAGES.LOGOUT_SUCCESS);
    } catch (error) {
      request.log.error(error, 'Error during logout');
      return sendInternalError(reply, 'Logout failed');
    }
  }

  // GET /auth/me - Get current user info
  static async getCurrentUser(request: FastifyRequest, reply: FastifyReply) {
    try {
      const authUser = request.user as AuthUser | undefined;
      if (!authUser) {
        return sendUnauthorized(reply, AUTH_MESSAGES.TOKEN_REQUIRED);
      }

      // Get fresh user data from database
      const user = await UserService.getUserById(authUser.id);
      if (!user) {
        return sendUnauthorized(reply, AUTH_MESSAGES.TOKEN_INVALID);
      }

      return sendSuccess(
        reply,
        { user },
        'User information retrieved successfully'
      );
    } catch (error) {
      request.log.error(error, 'Error getting current user');
      return sendInternalError(reply, 'Failed to get user information');
    }
  }

  // POST /auth/refresh - Refresh JWT token
  static async refreshToken(request: FastifyRequest, reply: FastifyReply) {
    try {
      const authUser = request.user as AuthUser | undefined;
      if (!authUser) {
        return sendUnauthorized(reply, AUTH_MESSAGES.TOKEN_REQUIRED);
      }

      // Get fresh user data from database
      const user = await UserService.getUserById(authUser.id);
      if (!user) {
        return sendUnauthorized(reply, AUTH_MESSAGES.TOKEN_INVALID);
      }

      // Generate new JWT token
      const token = request.server.jwt.sign({
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        isActive: user.isActive,
      });

      // Set new cookie
      reply.setCookie('token', token, {
        httpOnly: true,
        secure: env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
      });

      return sendSuccess(reply, { token }, 'Token refreshed successfully');
    } catch (error) {
      request.log.error(error, 'Error refreshing token');
      return sendInternalError(reply, 'Failed to refresh token');
    }
  }
}
