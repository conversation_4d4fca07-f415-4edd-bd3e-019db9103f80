{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "baseUrl": "./src", "paths": {"@/*": ["./*"], "@/config/*": ["./config/*"], "@/controllers/*": ["./controllers/*"], "@/services/*": ["./services/*"], "@/repositories/*": ["./repositories/*"], "@/models/*": ["./models/*"], "@/utils/*": ["./utils/*"], "@/types/*": ["./types/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "tests"]}