import { FastifyInstance } from 'fastify';
import fp from 'fastify-plugin';
import { auth } from '@/lib/auth';
import { validateEnv } from '@/config/env';

async function betterAuthPlugin(fastify: FastifyInstance) {
  const env = validateEnv();

  // Add CORS headers specifically for auth routes
  fastify.addHook('onRequest', async (request, reply) => {
    if (request.url.startsWith('/api/auth')) {
      reply.header('Access-Control-Allow-Origin', env.FRONTEND_URL);
      reply.header('Access-Control-Allow-Credentials', 'true');
      reply.header(
        'Access-Control-Allow-Methods',
        'GET, POST, PUT, DELETE, OPTIONS'
      );
      reply.header(
        'Access-Control-Allow-Headers',
        'Content-Type, Authorization, Cookie'
      );

      if (request.method === 'OPTIONS') {
        reply.status(200).send();
        return;
      }
    }
  });

  fastify.all('/api/auth/*', async (request, reply) => {
    const url = new URL(request.url, `http://${request.headers.host}`);

    const requestInit: RequestInit = {
      method: request.method,
      headers: request.headers as Record<string, string>,
    };

    if (request.method !== 'GET' && request.body) {
      requestInit.body = JSON.stringify(request.body);
    }

    const webRequest = new Request(url, requestInit);

    const response = await auth.handler(webRequest);

    reply.status(response.status);
    response.headers.forEach((value, key) => reply.header(key, value));
    return response.text();
  });

  fastify.addHook('preHandler', async request => {
    (request as any).auth = auth;
  });
}

export default fp(betterAuthPlugin, {
  name: 'better-auth',
});

declare module 'fastify' {
  interface FastifyRequest {
    auth: typeof auth;
  }
}
