import { buildServer } from '../../src/index';
import { FastifyInstance } from 'fastify';

describe('Health Routes', () => {
  let server: FastifyInstance;

  beforeAll(async () => {
    server = await buildServer();
  });

  afterAll(async () => {
    await server.close();
  });

  describe('GET /api/v1/health', () => {
    it('should return health status', async () => {
      const response = await server.inject({
        method: 'GET',
        url: '/api/v1/health',
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.body);
      expect(body.success).toBe(true);
      expect(body.data.status).toBe('healthy');
      expect(body.data).toHaveProperty('timestamp');
      expect(body.data).toHaveProperty('uptime');
    });
  });

  describe('GET /ping', () => {
    it('should return pong for backward compatibility', async () => {
      const response = await server.inject({
        method: 'GET',
        url: '/ping',
      });

      expect(response.statusCode).toBe(200);
      const body = JSON.parse(response.body);
      expect(body.message).toBe('pong');
    });
  });
});