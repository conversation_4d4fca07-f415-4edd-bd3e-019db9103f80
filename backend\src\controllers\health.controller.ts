import { FastifyRequest, FastifyReply } from 'fastify';
import { sendSuccess } from '@/utils/response';

export class HealthController {
  static async getHealth(_request: FastifyRequest, reply: FastifyReply) {
    const healthData = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env['NODE_ENV'],
      version: process.env['npm_package_version'] || '1.0.0',
    };

    sendSuccess(reply, healthData, 'Service is healthy');
  }

  static async getReadiness(_request: FastifyRequest, reply: FastifyReply) {
    // Add database connectivity checks here
    const readinessData = {
      status: 'ready',
      checks: {
        database: 'connected', // Replace with actual DB check
        redis: 'connected', // Replace with actual Redis check
      },
    };

    sendSuccess(reply, readinessData, 'Service is ready');
  }
}
