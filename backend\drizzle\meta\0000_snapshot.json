{"id": "81ecbee5-4c3d-477a-b297-79f21d979ac9", "prevId": "********-0000-0000-0000-************", "version": "7", "dialect": "postgresql", "tables": {"public.account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "account_id": {"name": "account_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"account_user_id_users_id_fk": {"name": "account_user_id_users_id_fk", "tableFrom": "account", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.applications": {"name": "applications", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "campaign_id": {"name": "campaign_id", "type": "uuid", "primaryKey": false, "notNull": true}, "influencer_id": {"name": "influencer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "application_statuses", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "pitch": {"name": "pitch", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"applications_campaign_id_campaigns_id_fk": {"name": "applications_campaign_id_campaigns_id_fk", "tableFrom": "applications", "tableTo": "campaigns", "columnsFrom": ["campaign_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "applications_influencer_id_influencer_profiles_id_fk": {"name": "applications_influencer_id_influencer_profiles_id_fk", "tableFrom": "applications", "tableTo": "influencer_profiles", "columnsFrom": ["influencer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.brand_profiles": {"name": "brand_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "company_name": {"name": "company_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "industry": {"name": "industry", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"brand_profiles_user_id_users_id_fk": {"name": "brand_profiles_user_id_users_id_fk", "tableFrom": "brand_profiles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"brand_profiles_user_id_unique": {"name": "brand_profiles_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.campaigns": {"name": "campaigns", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "brand_id": {"name": "brand_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "requirements": {"name": "requirements", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'{}'::jsonb"}, "budget": {"name": "budget", "type": "integer", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "campaign_statuses", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'open'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"campaigns_brand_id_brand_profiles_id_fk": {"name": "campaigns_brand_id_brand_profiles_id_fk", "tableFrom": "campaigns", "tableTo": "brand_profiles", "columnsFrom": ["brand_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.influencer_profiles": {"name": "influencer_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "niche": {"name": "niche", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "avg_engagement_rate": {"name": "avg_engagement_rate", "type": "numeric", "primaryKey": false, "notNull": false}, "total_followers": {"name": "total_followers", "type": "integer", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "profile_picture_url": {"name": "profile_picture_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"influencer_profiles_user_id_users_id_fk": {"name": "influencer_profiles_user_id_users_id_fk", "tableFrom": "influencer_profiles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"influencer_profiles_user_id_unique": {"name": "influencer_profiles_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.languages": {"name": "languages", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "locale_code": {"name": "locale_code", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"languages_name_unique": {"name": "languages_name_unique", "nullsNotDistinct": false, "columns": ["name"]}, "languages_locale_code_unique": {"name": "languages_locale_code_unique", "nullsNotDistinct": false, "columns": ["locale_code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.posts": {"name": "posts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "social_media_account_id": {"name": "social_media_account_id", "type": "uuid", "primaryKey": false, "notNull": true}, "external_post_id": {"name": "external_post_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "caption": {"name": "caption", "type": "text", "primaryKey": false, "notNull": false}, "hashtags": {"name": "hashtags", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "format": {"name": "format", "type": "post_formats", "typeSchema": "public", "primaryKey": false, "notNull": false}, "likes": {"name": "likes", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "comments": {"name": "comments", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "shares": {"name": "shares", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "published_at": {"name": "published_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"posts_social_media_account_id_social_media_accounts_id_fk": {"name": "posts_social_media_account_id_social_media_accounts_id_fk", "tableFrom": "posts", "tableTo": "social_media_accounts", "columnsFrom": ["social_media_account_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.predictions": {"name": "predictions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "influencer_id": {"name": "influencer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "input_text": {"name": "input_text", "type": "text", "primaryKey": false, "notNull": false}, "predicted_likes": {"name": "predicted_likes", "type": "integer", "primaryKey": false, "notNull": false}, "predicted_engagement_rate": {"name": "predicted_engagement_rate", "type": "numeric", "primaryKey": false, "notNull": false}, "topics": {"name": "topics", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "sentiment": {"name": "sentiment", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "model_version": {"name": "model_version", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"predictions_influencer_id_influencer_profiles_id_fk": {"name": "predictions_influencer_id_influencer_profiles_id_fk", "tableFrom": "predictions", "tableTo": "influencer_profiles", "columnsFrom": ["influencer_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"session_user_id_users_id_fk": {"name": "session_user_id_users_id_fk", "tableFrom": "session", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"session_token_unique": {"name": "session_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.social_media_accounts": {"name": "social_media_accounts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "platform": {"name": "platform", "type": "platforms", "typeSchema": "public", "primaryKey": false, "notNull": true}, "external_account_id": {"name": "external_account_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"social_media_accounts_user_id_users_id_fk": {"name": "social_media_accounts_user_id_users_id_fk", "tableFrom": "social_media_accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscriptions": {"name": "subscriptions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "stripe_subscription_id": {"name": "stripe_subscription_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "tier": {"name": "tier", "type": "subscription_tiers", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'free'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "current_period_start": {"name": "current_period_start", "type": "timestamp", "primaryKey": false, "notNull": true}, "current_period_end": {"name": "current_period_end", "type": "timestamp", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"subscriptions_user_id_users_id_fk": {"name": "subscriptions_user_id_users_id_fk", "tableFrom": "subscriptions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.transactions": {"name": "transactions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "application_id": {"name": "application_id", "type": "uuid", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "transaction_types", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "transaction_statuses", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"transactions_user_id_users_id_fk": {"name": "transactions_user_id_users_id_fk", "tableFrom": "transactions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "transactions_application_id_applications_id_fk": {"name": "transactions_application_id_applications_id_fk", "tableFrom": "transactions", "tableTo": "applications", "columnsFrom": ["application_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_languages": {"name": "user_languages", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "language_id": {"name": "language_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"user_languages_user_id_users_id_fk": {"name": "user_languages_user_id_users_id_fk", "tableFrom": "user_languages", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_languages_language_id_languages_id_fk": {"name": "user_languages_language_id_languages_id_fk", "tableFrom": "user_languages", "tableTo": "languages", "columnsFrom": ["language_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"user_languages_user_id_language_id_pk": {"name": "user_languages_user_id_language_id_pk", "columns": ["user_id", "language_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "password_hash": {"name": "password_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "google_id": {"name": "google_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "user_roles", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'user'"}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.application_statuses": {"name": "application_statuses", "schema": "public", "values": ["pending", "accepted", "rejected"]}, "public.campaign_statuses": {"name": "campaign_statuses", "schema": "public", "values": ["open", "closed", "filled"]}, "public.platforms": {"name": "platforms", "schema": "public", "values": ["instagram", "youtube", "x"]}, "public.post_formats": {"name": "post_formats", "schema": "public", "values": ["post", "reel", "story"]}, "public.subscription_tiers": {"name": "subscription_tiers", "schema": "public", "values": ["free", "premium"]}, "public.transaction_statuses": {"name": "transaction_statuses", "schema": "public", "values": ["pending", "completed", "failed"]}, "public.transaction_types": {"name": "transaction_types", "schema": "public", "values": ["commission", "subscription"]}, "public.user_roles": {"name": "user_roles", "schema": "public", "values": ["user", "influencer", "brand", "agency", "admin"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}