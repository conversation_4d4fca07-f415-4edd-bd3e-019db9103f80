// Generated by React Router

import "react-router"

declare module "react-router" {
  interface Register {
    pages: Pages
    routeFiles: RouteFiles
  }
}

type Pages = {
  "/": {
    params: {};
  };
  "/auth/login": {
    params: {};
  };
  "/auth/register": {
    params: {};
  };
};

type RouteFiles = {
  "root.tsx": {
    id: "root";
    page: "/" | "/auth/login" | "/auth/register";
  };
  "routes/home.tsx": {
    id: "routes/home";
    page: "/";
  };
  "routes/auth/login.tsx": {
    id: "routes/auth/login";
    page: "/auth/login";
  };
  "routes/auth/register.tsx": {
    id: "routes/auth/register";
    page: "/auth/register";
  };
};