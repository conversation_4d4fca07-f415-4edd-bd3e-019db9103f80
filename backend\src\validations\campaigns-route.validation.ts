// JSON Schema definitions for Campaigns routes - Body and Query validation only

// GET /campaigns - Get all campaigns with pagination and filtering
export const getCampaignsSchema = {
  querystring: {
    type: 'object',
    properties: {
      page: { type: 'number', minimum: 1, default: 1 },
      limit: { type: 'number', minimum: 1, maximum: 100, default: 10 },
      search: { type: 'string' },
      status: { 
        type: 'string', 
        enum: ['draft', 'active', 'paused', 'completed', 'cancelled'] 
      },
      budget_min: { type: 'number', minimum: 0 },
      budget_max: { type: 'number', minimum: 0 },
    },
  },
};

// GET /campaigns/:id - Get campaign by ID
export const getCampaignByIdSchema = {
  params: {
    type: 'object',
    properties: {
      id: { type: 'string', format: 'uuid' },
    },
    required: ['id'],
  },
};

// POST /campaigns - Create a new campaign
export const createCampaignSchema = {
  body: {
    type: 'object',
    properties: {
      title: { type: 'string', minLength: 1, maxLength: 200 },
      description: { type: 'string', maxLength: 1000 },
      budget: { type: 'number', minimum: 0 },
      startDate: { type: 'string', format: 'date' },
      endDate: { type: 'string', format: 'date' },
      targetAudience: {
        type: 'object',
        properties: {
          ageRange: {
            type: 'object',
            properties: {
              min: { type: 'number', minimum: 13, maximum: 100 },
              max: { type: 'number', minimum: 13, maximum: 100 },
            },
          },
          gender: { type: 'string', enum: ['male', 'female', 'all'] },
          interests: {
            type: 'array',
            items: { type: 'string' },
            maxItems: 10,
          },
        },
      },
      requirements: {
        type: 'object',
        properties: {
          minFollowers: { type: 'number', minimum: 0 },
          platforms: {
            type: 'array',
            items: { 
              type: 'string', 
              enum: ['instagram', 'tiktok', 'youtube', 'twitter', 'facebook'] 
            },
            minItems: 1,
          },
        },
      },
    },
    required: ['title', 'description', 'budget', 'startDate', 'endDate'],
  },
};

// PUT /campaigns/:id - Update campaign by ID
export const updateCampaignSchema = {
  params: {
    type: 'object',
    properties: {
      id: { type: 'string', format: 'uuid' },
    },
    required: ['id'],
  },
  body: {
    type: 'object',
    properties: {
      title: { type: 'string', minLength: 1, maxLength: 200 },
      description: { type: 'string', maxLength: 1000 },
      budget: { type: 'number', minimum: 0 },
      startDate: { type: 'string', format: 'date' },
      endDate: { type: 'string', format: 'date' },
      status: { 
        type: 'string', 
        enum: ['draft', 'active', 'paused', 'completed', 'cancelled'] 
      },
      targetAudience: {
        type: 'object',
        properties: {
          ageRange: {
            type: 'object',
            properties: {
              min: { type: 'number', minimum: 13, maximum: 100 },
              max: { type: 'number', minimum: 13, maximum: 100 },
            },
          },
          gender: { type: 'string', enum: ['male', 'female', 'all'] },
          interests: {
            type: 'array',
            items: { type: 'string' },
            maxItems: 10,
          },
        },
      },
      requirements: {
        type: 'object',
        properties: {
          minFollowers: { type: 'number', minimum: 0 },
          platforms: {
            type: 'array',
            items: { 
              type: 'string', 
              enum: ['instagram', 'tiktok', 'youtube', 'twitter', 'facebook'] 
            },
            minItems: 1,
          },
        },
      },
    },
    // No required fields for update - all optional
  },
};

// DELETE /campaigns/:id - Delete campaign by ID
export const deleteCampaignSchema = {
  params: {
    type: 'object',
    properties: {
      id: { type: 'string', format: 'uuid' },
    },
    required: ['id'],
  },
};
