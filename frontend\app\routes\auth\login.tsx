import { use<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "react-hook-form";
import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { signIn } from "~/lib/auth-client";
import { useNavigate } from "react-router";
import type { Route } from "./+types/login";
import { Button } from "~/components/ui/button";
import { Loader2Icon } from "lucide-react";
import { loginSchema, type LoginSchema } from "~/validations/login";
import {
	Form,
	FormField,
	FormItem,
	FormLabel,
	FormControl,
	FormMessage,
	FormDescription,
} from "~/components/ui/form";
import { Input } from "~/components/ui/input";

export function meta({}: Route.MetaArgs) {
	return [
		{ title: "Login - Influencer Marketing" },
		{ name: "description", content: "Sign in to your account" },
	];
}

export default function Login() {
	const navigate = useNavigate();
	const methods = useForm<LoginSchema>({
		resolver: zodResolver(loginSchema),
	});

	const {
		handleSubmit,
		formState: { isSubmitting },
	} = methods;

	const onSubmit = async (data: LoginSchema) => {
		try {
			await signIn.email({
				email: data.email,
				password: data.password,
			});
			navigate("/");
		} catch (error) {
			console.error("Login failed:", error);
		}
	};

	const handleGoogleLogin = async () => {
		try {
			await signIn.social({
				provider: "google",
				callbackURL: import.meta.env.VITE_FRONTEND_URL,
			});
		} catch (error) {
			console.error("Google login failed:", error);
		}
	};

	return (
		<div className="min-h-screen flex items-center justify-center bg-gray-50">
			<div className="max-w-md w-full space-y-8">
				<div>
					<h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
						Sign in to your account
					</h2>
				</div>
				<FormProvider {...methods}>
					<Form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)} noValidate>
						<FormField
							name="email"
							render={({ value, onChange, onBlur, ref, error }) => (
								<FormItem>
									<FormLabel htmlFor="email">Email address</FormLabel>
									<FormControl>
										<Input
											id="email"
											type="email"
											autoComplete="email"
											placeholder="Email address"
											value={value}
											onChange={onChange}
											onBlur={onBlur}
											ref={ref}
										/>
									</FormControl>
									<FormMessage>{error?.message}</FormMessage>
								</FormItem>
							)}
						/>
						<FormField
							name="password"
							render={({ value, onChange, onBlur, ref, error }) => (
								<FormItem>
									<FormLabel htmlFor="password">Password</FormLabel>
									<FormControl>
										<Input
											id="password"
											type="password"
											autoComplete="current-password"
											placeholder="Password"
											value={value}
											onChange={onChange}
											onBlur={onBlur}
											ref={ref}
										/>
									</FormControl>
									<FormMessage>{error?.message}</FormMessage>
								</FormItem>
							)}
						/>
						<div>
							<Button type="submit" className="min-w-32" disabled={isSubmitting}>
								{isSubmitting ? (
									<>
										<Loader2Icon className="animate-spin" /> <span>Signing in</span>
									</>
								) : (
									"Sign in"
								)}
							</Button>
						</div>
						<div>
							<button
								type="button"
								onClick={handleGoogleLogin}
								className="group relative w-full flex justify-center py-2 px-4 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
							>
								Sign in with Google
							</button>
						</div>
					</Form>
				</FormProvider>
			</div>
		</div>
	);
}
