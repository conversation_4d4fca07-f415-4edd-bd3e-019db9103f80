import bcrypt from 'bcrypt';
import { users } from '@/models/schema';
import { UserRepository } from '@/repositories/user.repository';
import { AUTH_MESSAGES } from '@/constants';

export interface CreateUserData {
  email: string;
  name: string;
  role?: typeof users.$inferInsert.role;
  passwordHash?: string;
  googleId?: string;
  stripeCustomerId?: string;
}

export interface CreateUserWithPasswordData
  extends Omit<CreateUserData, 'passwordHash'> {
  password: string;
}

export interface GoogleUserData {
  email: string;
  name: string;
  googleId: string;
  role?: typeof users.$inferInsert.role;
}

export class UserService {
  private static readonly SALT_ROUNDS = 12;

  /**
   * Hash a password using bcrypt
   */
  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, this.SALT_ROUNDS);
  }

  /**
   * Verify a password against a hash
   */
  static async verifyPassword(
    password: string,
    hash: string
  ): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  /**
   * Create a user with email/password authentication
   */
  static async createUserWithPassword(userData: CreateUserWithPasswordData) {
    // Check if user already exists
    const existingUser = await UserRepository.findByEmail(userData.email, true);
    if (existingUser.length > 0) {
      throw new Error(AUTH_MESSAGES.EMAIL_ALREADY_EXISTS);
    }

    // Hash the password
    const passwordHash = await this.hashPassword(userData.password);

    // Create user data
    const newUserData: typeof users.$inferInsert = {
      email: userData.email,
      name: userData.name,
      passwordHash,
      role: userData.role || 'user',
      stripeCustomerId: userData.stripeCustomerId,
      isActive: true,
    };

    return UserRepository.create(newUserData);
  }

  /**
   * Create or update a user with Google authentication
   */
  static async createOrUpdateGoogleUser(googleData: GoogleUserData) {
    // Check if user exists by email
    const existingUserByEmail = await UserRepository.findByEmail(
      googleData.email,
      true
    );

    if (existingUserByEmail.length > 0) {
      const user = existingUserByEmail[0];

      // If user exists but doesn't have Google ID, update it
      if (user && !user.googleId) {
        return UserRepository.update(user.id, {
          googleId: googleData.googleId,
          isActive: true, // Reactivate if inactive
        });
      }

      // User exists with Google ID, just return the user
      return existingUserByEmail;
    }

    // Check if user exists by Google ID
    const existingUserByGoogleId = await UserRepository.findByGoogleId(
      googleData.googleId
    );
    if (existingUserByGoogleId.length > 0) {
      return existingUserByGoogleId;
    }

    // Create new user with Google authentication
    const newUserData: typeof users.$inferInsert = {
      email: googleData.email,
      name: googleData.name,
      googleId: googleData.googleId,
      role: googleData.role || 'user',
      isActive: true,
    };

    return UserRepository.create(newUserData);
  }

  /**
   * Authenticate user with email and password
   */
  static async authenticateUser(email: string, password: string) {
    // Find user by email (only active users)
    const users = await UserRepository.findByEmail(email, false);
    if (users.length === 0) {
      throw new Error(AUTH_MESSAGES.INVALID_CREDENTIALS);
    }

    const user = users[0];
    if (!user) {
      throw new Error(AUTH_MESSAGES.INVALID_CREDENTIALS);
    }

    // Check if user has a password hash (not Google-only user)
    if (!user.passwordHash) {
      throw new Error(AUTH_MESSAGES.INVALID_CREDENTIALS);
    }

    // Verify password
    const isValidPassword = await this.verifyPassword(
      password,
      user.passwordHash
    );
    if (!isValidPassword) {
      throw new Error(AUTH_MESSAGES.INVALID_CREDENTIALS);
    }

    // Return user without password hash
    const { passwordHash, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  /**
   * Get user by ID (active users only by default)
   */
  static async getUserById(id: string, includeInactive = false) {
    const users = await UserRepository.findById(id, includeInactive);
    if (users.length === 0) {
      return null;
    }

    const user = users[0];
    if (!user) {
      return null;
    }
    // Remove password hash from response
    const { passwordHash, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  /**
   * Update user profile
   */
  static async updateUser(
    id: string,
    updateData: Partial<typeof users.$inferInsert>
  ) {
    // If password is being updated, hash it
    if (
      updateData.passwordHash &&
      typeof updateData.passwordHash === 'string'
    ) {
      updateData.passwordHash = await this.hashPassword(
        updateData.passwordHash
      );
    }

    const updatedUsers = await UserRepository.update(id, updateData);
    if (updatedUsers.length === 0) {
      return null;
    }

    const user = updatedUsers[0];
    if (!user) {
      return null;
    }
    // Remove password hash from response
    const { passwordHash, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  /**
   * Soft delete user (set isActive to false)
   */
  static async deleteUser(id: string) {
    return UserRepository.delete(id);
  }

  /**
   * Get all users with pagination
   */
  static async getAllUsers(page: number, limit: number, search?: string) {
    const result = await UserRepository.findAll(page, limit, search);

    // Remove password hashes from all users
    const usersWithoutPasswords = result.users.map(user => {
      const { passwordHash, ...userWithoutPassword } = user;
      return userWithoutPassword;
    });

    return {
      ...result,
      users: usersWithoutPasswords,
    };
  }
}
