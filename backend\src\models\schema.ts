import {
  pgTable,
  uuid,
  varchar,
  text,
  integer,
  timestamp,
  jsonb,
  boolean,
  pgEnum,
  numeric,
  primaryKey,
} from 'drizzle-orm/pg-core';
import { relations, sql } from 'drizzle-orm';

// ---- Enums ----
export const userRoles = pgEnum('user_roles', [
  'user',
  'influencer',
  'brand',
  'agency',
  'admin',
]);
export const subscriptionTiers = pgEnum('subscription_tiers', [
  'free',
  'premium',
]);
export const platforms = pgEnum('platforms', ['instagram', 'youtube', 'x']);
export const postFormats = pgEnum('post_formats', ['post', 'reel', 'story']);
export const campaignStatuses = pgEnum('campaign_statuses', [
  'open',
  'closed',
  'filled',
]);
export const applicationStatuses = pgEnum('application_statuses', [
  'pending',
  'accepted',
  'rejected',
]);
export const transactionTypes = pgEnum('transaction_types', [
  'commission',
  'subscription',
]);
export const transactionStatuses = pgEnum('transaction_statuses', [
  'pending',
  'completed',
  'failed',
]);

// ---- Users ----
export const users = pgTable('users', {
  id: varchar('id', { length: 255 }).primaryKey(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  emailVerified: boolean('email_verified').notNull().default(false),
  name: varchar('name', { length: 100 }).notNull(),
  image: varchar('image', { length: 255 }),
  passwordHash: varchar('password_hash', { length: 255 }),
  googleId: varchar('google_id', { length: 255 }),
  role: userRoles('role').notNull().default('user'),
  stripeCustomerId: varchar('stripe_customer_id', { length: 255 }),
  isActive: boolean('is_active').notNull().default(true),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// ---- Languages & User Languages (M‑N) ----
export const languages = pgTable('languages', {
  id: uuid('id').defaultRandom().primaryKey(),
  name: varchar('name', { length: 100 }).notNull().unique(),
  localeCode: varchar('locale_code', { length: 10 }).notNull().unique(),
});

export const userLanguages = pgTable(
  'user_languages',
  {
    userId: varchar('user_id', { length: 255 })
      .notNull()
      .references(() => users.id, { onDelete: 'cascade' }),
    languageId: uuid('language_id')
      .notNull()
      .references(() => languages.id, { onDelete: 'cascade' }),
  },
  table => ({
    pk: primaryKey({ columns: [table.userId, table.languageId] }),
  })
);

// ---- Social Accounts ----
export const socialMediaAccounts = pgTable('social_media_accounts', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: varchar('user_id', { length: 255 })
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  platform: platforms('platform').notNull(),
  externalAccountId: varchar('external_account_id', { length: 255 }).notNull(),
  accessToken: text('access_token').notNull(),
  refreshToken: text('refresh_token'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// ---- Posts ----
export const posts = pgTable('posts', {
  id: uuid('id').defaultRandom().primaryKey(),
  socialMediaAccountId: uuid('social_media_account_id')
    .notNull()
    .references(() => socialMediaAccounts.id, { onDelete: 'cascade' }),
  externalPostId: varchar('external_post_id', { length: 255 }).notNull(),
  caption: text('caption'),
  hashtags: jsonb('hashtags')
    .notNull()
    .default(sql`'[]'::jsonb`),
  format: postFormats('format'),
  likes: integer('likes').default(0),
  comments: integer('comments').default(0),
  shares: integer('shares').default(0),
  publishedAt: timestamp('published_at'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// ---- Influencer Profiles ----
export const influencerProfiles = pgTable('influencer_profiles', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: varchar('user_id', { length: 255 })
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' })
    .unique(),
  niche: varchar('niche', { length: 100 }),
  avgEngagementRate: numeric('avg_engagement_rate'),
  totalFollowers: integer('total_followers'),
  bio: text('bio'),
  profilePictureUrl: varchar('profile_picture_url', { length: 255 }),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// ---- Brand Profiles ----
export const brandProfiles = pgTable('brand_profiles', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: varchar('user_id', { length: 255 })
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' })
    .unique(),
  companyName: varchar('company_name', { length: 255 }).notNull(),
  industry: varchar('industry', { length: 100 }),
  description: text('description'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// ---- Campaigns ----
export const campaigns = pgTable('campaigns', {
  id: uuid('id').defaultRandom().primaryKey(),
  brandId: uuid('brand_id')
    .notNull()
    .references(() => brandProfiles.id, { onDelete: 'cascade' }),
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description'),
  requirements: jsonb('requirements')
    .notNull()
    .default(sql`'{}'::jsonb`),
  budget: integer('budget').notNull(),
  startDate: timestamp('start_date'),
  endDate: timestamp('end_date'),
  status: campaignStatuses('status').notNull().default('open'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// ---- Applications ----
export const applications = pgTable('applications', {
  id: uuid('id').defaultRandom().primaryKey(),
  campaignId: uuid('campaign_id')
    .notNull()
    .references(() => campaigns.id, { onDelete: 'cascade' }),
  influencerId: uuid('influencer_id')
    .notNull()
    .references(() => influencerProfiles.id, { onDelete: 'cascade' }),
  status: applicationStatuses('status').notNull().default('pending'),
  pitch: text('pitch'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// ---- Transactions ----
export const transactions = pgTable('transactions', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: varchar('user_id', { length: 255 })
    .notNull()
    .references(() => users.id),
  applicationId: uuid('application_id').references(() => applications.id),
  amount: integer('amount').notNull(),
  type: transactionTypes('type').notNull(),
  status: transactionStatuses('status').notNull().default('pending'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// ---- Better Auth Tables ----
export const sessions = pgTable('session', {
  id: varchar('id', { length: 255 }).primaryKey(),
  userId: varchar('user_id', { length: 255 })
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  expiresAt: timestamp('expires_at').notNull(),
  token: varchar('token', { length: 255 }).notNull().unique(),
  ipAddress: varchar('ip_address', { length: 255 }),
  userAgent: text('user_agent'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const accounts = pgTable('account', {
  id: varchar('id', { length: 255 }).primaryKey(),
  userId: varchar('user_id', { length: 255 })
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  accountId: varchar('account_id', { length: 255 }).notNull(),
  providerId: varchar('provider_id', { length: 255 }).notNull(),
  accessToken: text('access_token'),
  refreshToken: text('refresh_token'),
  idToken: text('id_token'),
  accessTokenExpiresAt: timestamp('access_token_expires_at'),
  refreshTokenExpiresAt: timestamp('refresh_token_expires_at'),
  scope: varchar('scope', { length: 255 }),
  password: varchar('password', { length: 255 }),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const verifications = pgTable('verification', {
  id: varchar('id', { length: 255 }).primaryKey(),
  identifier: varchar('identifier', { length: 255 }).notNull(),
  value: varchar('value', { length: 255 }).notNull(),
  expiresAt: timestamp('expires_at').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// ---- Subscriptions ----
export const subscriptions = pgTable('subscriptions', {
  id: uuid('id').defaultRandom().primaryKey(),
  userId: varchar('user_id', { length: 255 })
    .notNull()
    .references(() => users.id),
  stripeSubscriptionId: varchar('stripe_subscription_id', {
    length: 255,
  }).notNull(),
  tier: subscriptionTiers('tier').notNull().default('free'),
  status: varchar('status', { length: 50 }).notNull(),
  currentPeriodStart: timestamp('current_period_start').notNull(),
  currentPeriodEnd: timestamp('current_period_end').notNull(),
  isActive: boolean('is_active').notNull().default(true),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// ---- AI Predictions Log ----
export const predictions = pgTable('predictions', {
  id: uuid('id').defaultRandom().primaryKey(),
  influencerId: uuid('influencer_id')
    .notNull()
    .references(() => influencerProfiles.id),
  inputText: text('input_text'),
  predictedLikes: integer('predicted_likes'),
  predictedEngagementRate: numeric('predicted_engagement_rate'),
  topics: jsonb('topics')
    .notNull()
    .default(sql`'[]'::jsonb`),
  sentiment: varchar('sentiment', { length: 10 }), // 'positive', 'neutral', 'negative'
  modelVersion: varchar('model_version', { length: 50 }),
  createdAt: timestamp('created_at').notNull().defaultNow(),
});

export const usersRelations = relations(users, ({ many, one }) => ({
  socialMediaAccounts: many(socialMediaAccounts),
  influencerProfile: one(influencerProfiles, {
    fields: [users.id],
    references: [influencerProfiles.userId],
  }),
  brandProfile: one(brandProfiles, {
    fields: [users.id],
    references: [brandProfiles.userId],
  }),
  subscriptions: many(subscriptions),
  transactions: many(transactions),
  userLanguages: many(userLanguages),
}));

export const socialMediaAccountsRelations = relations(
  socialMediaAccounts,
  ({ one, many }) => ({
    user: one(users, {
      fields: [socialMediaAccounts.userId],
      references: [users.id],
    }),
    posts: many(posts),
  })
);

export const postsRelations = relations(posts, ({ one }) => ({
  socialMediaAccount: one(socialMediaAccounts, {
    fields: [posts.socialMediaAccountId],
    references: [socialMediaAccounts.id],
  }),
}));

export const influencerProfilesRelations = relations(
  influencerProfiles,
  ({ one, many }) => ({
    user: one(users, {
      fields: [influencerProfiles.userId],
      references: [users.id],
    }),
    applications: many(applications),
    predictions: many(predictions),
  })
);

export const brandProfilesRelations = relations(
  brandProfiles,
  ({ one, many }) => ({
    user: one(users, {
      fields: [brandProfiles.userId],
      references: [users.id],
    }),
    campaigns: many(campaigns),
  })
);

export const campaignsRelations = relations(campaigns, ({ one, many }) => ({
  brand: one(brandProfiles, {
    fields: [campaigns.brandId],
    references: [brandProfiles.id],
  }),
  applications: many(applications),
}));

export const applicationsRelations = relations(applications, ({ one }) => ({
  campaign: one(campaigns, {
    fields: [applications.campaignId],
    references: [campaigns.id],
  }),
  influencer: one(influencerProfiles, {
    fields: [applications.influencerId],
    references: [influencerProfiles.id],
  }),
  transactions: one(transactions, {
    fields: [applications.id],
    references: [transactions.applicationId],
  }),
}));

export const transactionsRelations = relations(transactions, ({ one }) => ({
  user: one(users, {
    fields: [transactions.userId],
    references: [users.id],
  }),
  application: one(applications, {
    fields: [transactions.applicationId],
    references: [applications.id],
  }),
}));

export const subscriptionsRelations = relations(subscriptions, ({ one }) => ({
  user: one(users, {
    fields: [subscriptions.userId],
    references: [users.id],
  }),
}));

export const languagesRelations = relations(languages, ({ many }) => ({
  users: many(userLanguages),
}));

export const userLanguagesRelations = relations(userLanguages, ({ one }) => ({
  user: one(users, {
    fields: [userLanguages.userId],
    references: [users.id],
  }),
  language: one(languages, {
    fields: [userLanguages.languageId],
    references: [languages.id],
  }),
}));

export const predictionsRelations = relations(predictions, ({ one }) => ({
  influencer: one(influencerProfiles, {
    fields: [predictions.influencerId],
    references: [influencerProfiles.id],
  }),
}));
